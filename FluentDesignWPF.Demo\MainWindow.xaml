﻿<Window x:Class="FluentDesignWPF.Demo.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FluentDesignWPF.Demo"
        mc:Ignorable="d"
        Title="FluentDesignWPF 设计系统演示"
        Height="800"
        Width="1400"
        Background="{DynamicResource ThemeBackgroundPrimaryBrush}"
        Foreground="{DynamicResource ThemeTextPrimaryBrush}">

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧导航栏 -->
        <Border Grid.Column="0"
                Background="{DynamicResource ThemeBackgroundSecondaryBrush}"
                BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                BorderThickness="0,0,1,0">
            <StackPanel Margin="16">
                <TextBlock Text="FluentDesignWPF"
                           Style="{StaticResource Title1TextStyle}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="设计系统演示"
                           Style="{StaticResource Caption1TextStyle}"
                           Margin="0,0,0,24"/>

                <Button x:Name="ThemeToggleButton"
                        Content="切换主题"
                        Padding="12"
                        Margin="0,0,0,24"
                        Click="ThemeToggleButton_Click"/>

                <TextBlock Text="演示页面"
                           Style="{StaticResource Label2TextStyle}"
                           Margin="0,0,0,8"/>

                <Button x:Name="ColorsPageButton"
                        Content="色彩系统"
                        Padding="8"
                        Margin="0,0,0,8"
                        HorizontalAlignment="Stretch"
                        Click="ColorsPageButton_Click"/>

                <Button x:Name="TypographyPageButton"
                        Content="字体系统"
                        Padding="8"
                        Margin="0,0,0,8"
                        HorizontalAlignment="Stretch"
                        Click="TypographyPageButton_Click"/>

                <Button x:Name="TextBlockPageButton"
                        Content="TextBlock控件"
                        Padding="8"
                        Margin="0,0,0,8"
                        HorizontalAlignment="Stretch"
                        Click="TextBlockPageButton_Click"/>
            </StackPanel>
        </Border>

        <!-- 右侧内容区域 -->
        <ContentControl x:Name="MainContentControl" Grid.Column="1">
            <!-- 默认显示色彩系统 -->
            <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                <StackPanel Margin="32">
                    <TextBlock Text="FluentDesignWPF 色彩系统演示"
                               Style="{StaticResource Headline2TextStyle}"
                               Margin="0,0,0,16"/>

                    <TextBlock Text="这是一个基于Microsoft Fluent Design System的WPF控件库色彩系统演示。"
                               Style="{StaticResource Body1TextStyle}"
                               Margin="0,0,0,32"/>

                    <!-- 主题色彩展示 -->
                    <TextBlock Text="主题色彩 (Primary Colors)"
                               Style="{StaticResource Title2TextStyle}"
                               Margin="0,0,0,16"/>

                    <UniformGrid Columns="5" Margin="0,0,0,32">
                        <Border Background="{StaticResource PrimaryBrush100}"
                                Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="100"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="Black"
                                       Style="{StaticResource Label2TextStyle}"/>
                        </Border>
                        <Border Background="{StaticResource PrimaryBrush300}"
                                Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="300"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       Style="{StaticResource Label2TextStyle}"/>
                        </Border>
                        <Border Background="{StaticResource PrimaryBrush500}"
                                Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="500"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       Style="{StaticResource Label2TextStyle}"/>
                        </Border>
                        <Border Background="{StaticResource PrimaryBrush700}"
                                Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="700"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       Style="{StaticResource Label2TextStyle}"/>
                        </Border>
                        <Border Background="{StaticResource PrimaryBrush900}"
                                Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="900"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       Style="{StaticResource Label2TextStyle}"/>
                        </Border>
                    </UniformGrid>

                    <!-- 语义色彩展示 -->
                    <TextBlock Text="语义色彩 (Semantic Colors)"
                               Style="{StaticResource Title2TextStyle}"
                               Margin="0,0,0,16"/>

                    <UniformGrid Columns="4" Margin="0,0,0,32">
                        <Border Background="{StaticResource SuccessBrush}"
                                Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="Success"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       Style="{StaticResource Label2TextStyle}"/>
                        </Border>
                        <Border Background="{StaticResource WarningBrush}"
                                Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="Warning"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="Black"
                                       Style="{StaticResource Label2TextStyle}"/>
                        </Border>
                        <Border Background="{StaticResource ErrorBrush}"
                                Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="Error"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       Style="{StaticResource Label2TextStyle}"/>
                        </Border>
                        <Border Background="{StaticResource InfoBrush}"
                                Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="Info"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       Style="{StaticResource Label2TextStyle}"/>
                        </Border>
                    </UniformGrid>

                    <!-- 主题相关色彩展示 -->
                    <TextBlock Text="主题相关色彩 (Theme Colors)"
                               Style="{StaticResource Title2TextStyle}"
                               Margin="0,0,0,16"/>

                    <UniformGrid Columns="3" Margin="0,0,0,32">
                        <Border Background="{DynamicResource ThemeBackgroundPrimaryBrush}"
                                BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                                BorderThickness="1"
                                Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="主背景"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource ThemeTextPrimaryBrush}"
                                       Style="{StaticResource Label2TextStyle}"/>
                        </Border>
                        <Border Background="{DynamicResource ThemeBackgroundCardBrush}"
                                BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                                BorderThickness="1"
                                Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="卡片背景"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource ThemeTextPrimaryBrush}"
                                       Style="{StaticResource Label2TextStyle}"/>
                        </Border>
                        <Border Background="{DynamicResource ThemeBackgroundSecondaryBrush}"
                                BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                                BorderThickness="1"
                                Height="80" Margin="4" CornerRadius="{StaticResource CornerRadiusNormal}">
                            <TextBlock Text="次背景"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource ThemeTextPrimaryBrush}"
                                       Style="{StaticResource Label2TextStyle}"/>
                        </Border>
                    </UniformGrid>
                </StackPanel>
            </ScrollViewer>
        </ContentControl>
    </Grid>
</Window>
