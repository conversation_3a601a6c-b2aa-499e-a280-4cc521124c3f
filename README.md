# FluentDesignWPF 控件库

一个基于 Microsoft Fluent Design System 的现代化 WPF 控件库，提供完整的色彩系统、字体系统和主题切换功能。

## ✨ 特性

- 🎨 **完整的色彩系统** - 基于 Fluent Design System 的色彩规范
- 🔤 **完整的字体系统** - 从Caption到Display的全套字体层次结构
- 🌓 **主题切换** - 支持深色和浅色主题动态切换
- 🎯 **语义化设计** - 成功、警告、错误、信息等语义色彩和字体样式
- ♿ **无障碍支持** - 符合 WCAG 对比度标准
- 📱 **响应式设计** - 支持不同 DPI 和屏幕尺寸
- 🔧 **易于集成** - 简单的 XAML 引用即可使用

## 🚀 快速开始

### 安装

1. 克隆或下载此仓库
2. 在您的 WPF 项目中添加对 `FluentDesignWPF` 项目的引用

### 基本使用

1. **引用资源字典**

在您的 `App.xaml` 中添加：

```xml
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <!-- 引用FluentDesignWPF控件库的完整资源 -->
            <ResourceDictionary Source="pack://application:,,,/FluentDesignWPF;component/Themes/Generic.xaml"/>
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

2. **使用色彩和字体资源**

```xml
<Window Background="{DynamicResource ThemeBackgroundPrimaryBrush}"
        Foreground="{DynamicResource ThemeTextPrimaryBrush}">
    <Grid>
        <!-- 使用字体样式 -->
        <TextBlock Text="Hello FluentDesignWPF!"
                   Style="{StaticResource Headline2TextStyle}"/>

        <!-- 使用语义化字体样式 -->
        <TextBlock Text="成功信息"
                   Style="{StaticResource SuccessTextStyle}"/>

        <Button Content="主要按钮"
                Background="{StaticResource PrimaryBrush}"
                Foreground="White"/>

        <Border Background="{DynamicResource ThemeBackgroundCardBrush}"
                BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                BorderThickness="1"
                CornerRadius="4">
            <TextBlock Text="卡片标题"
                       Style="{StaticResource Title3TextStyle}"/>
            <TextBlock Text="卡片内容"
                       Style="{StaticResource Body2TextStyle}"/>
        </Border>
    </Grid>
</Window>
```

3. **主题切换**

```csharp
using FluentDesignWPF.Themes;

// 切换到深色主题
ThemeManager.SetTheme(Theme.Dark);

// 切换到浅色主题
ThemeManager.SetTheme(Theme.Light);

// 在深色和浅色之间切换
ThemeManager.ToggleTheme();
```

## 🎨 设计系统

### 色彩系统

#### 主要色彩类别

- **主题色彩** - 蓝色系，用于主要操作和强调
- **语义色彩** - 成功(绿)、警告(黄)、错误(红)、信息(蓝)
- **中性色彩** - 灰色系，用于文本、边框、背景
- **主题相关色彩** - 根据当前主题动态变化

#### 色彩层级

每种色彩提供 50-900 的完整层级：

```xml
<!-- 主题色彩示例 -->
<Border Background="{StaticResource PrimaryBrush50}"/>   <!-- 最浅 -->
<Border Background="{StaticResource PrimaryBrush500}"/>  <!-- 标准 -->
<Border Background="{StaticResource PrimaryBrush900}"/>  <!-- 最深 -->
```

### 字体系统

#### 字体层次结构

- **Display** (52px-68px) - 品牌标题和重要展示
- **Headline** (28px-40px) - 页面标题和重要标题
- **Title** (18px-24px) - 章节标题和卡片标题
- **Label** (12px-16px) - 标签和按钮文本
- **Body** (14px-16px) - 正文内容
- **Caption** (10px-12px) - 说明文字和辅助信息

#### 字体样式示例

```xml
<!-- 字体层次示例 -->
<TextBlock Text="品牌标题" Style="{StaticResource Display1TextStyle}"/>
<TextBlock Text="页面标题" Style="{StaticResource Headline1TextStyle}"/>
<TextBlock Text="章节标题" Style="{StaticResource Title1TextStyle}"/>
<TextBlock Text="正文内容" Style="{StaticResource Body2TextStyle}"/>
<TextBlock Text="说明文字" Style="{StaticResource Caption1TextStyle}"/>

<!-- 语义化样式 -->
<TextBlock Text="成功信息" Style="{StaticResource SuccessTextStyle}"/>
<TextBlock Text="错误信息" Style="{StaticResource ErrorTextStyle}"/>
<TextBlock Text="代码显示" Style="{StaticResource MonospaceTextStyle}"/>
```

### 主题切换

```csharp
// 切换主题
ThemeManager.ToggleTheme();

// 设置特定主题
ThemeManager.SetTheme(Theme.Dark);

// 监听主题变化
ThemeManager.ThemeChanged += (sender, e) =>
{
    Console.WriteLine($"主题切换: {e.OldTheme} -> {e.NewTheme}");
};
```

## 🧩 控件库

### 基础控件

#### FluentTextBlock
现代化的文本显示控件，提供完整的 Typography 层次结构和语义化颜色支持。

**特性**：
- 完整的 Typography 层次结构（Display、Headline、Title、Body、Caption）
- 语义化颜色变体（Primary、Secondary、Success、Warning、Error、Info）
- 多种字重支持（Light、Regular、Medium、SemiBold、Bold）
- 深色/浅色主题自动适配
- WCAG 可访问性合规

**基本使用**：
```xml
<!-- 使用控件命名空间 -->
xmlns:controls="clr-namespace:FluentDesignWPF.Controls.Basic;assembly=FluentDesignWPF"

<!-- 基本文本显示 -->
<controls:FluentTextBlock Text="Hello World"/>

<!-- 使用属性设置样式 -->
<controls:FluentTextBlock Text="标题文本"
                          TypographyVariant="Title1"
                          ColorVariant="Primary"
                          FontWeightVariant="Medium"/>

<!-- 使用预定义样式 -->
<controls:FluentTextBlock Text="成功消息"
                          Style="{StaticResource FluentTextBlockSuccess}"/>
```

**文档**：[FluentTextBlock 详细文档](Docs/Controls/TextBlock.md)

### 即将推出的控件

- **FluentButton** - 现代化按钮控件
- **FluentTextBox** - 输入框控件
- **FluentComboBox** - 下拉选择控件
- **FluentCard** - 卡片容器控件
- **FluentNavigationView** - 导航视图控件

## 📁 项目结构

```
FluentDesignWPF/
├── Themes/                          # 主题和设计系统
│   ├── Colors.xaml                  # 色彩定义
│   ├── Brushes.xaml                 # 画刷资源
│   ├── Typography.xaml              # 字体系统
│   ├── ThemeManager.xaml            # 主题管理器资源
│   ├── ThemeManager.cs              # 主题管理器类
│   ├── Generic.xaml                 # 通用资源字典
│   ├── Controls/                    # 控件样式资源字典
│   │   └── TextBlock.xaml           # FluentTextBlock 样式
│   └── README.md                    # 设计系统文档
├── Controls/                        # 控件库
│   └── Basic/                       # 基础控件
│       ├── FluentTextBlock.cs       # FluentTextBlock 控件
│       └── Enums.cs                 # 枚举定义
└── FluentDesignWPF.csproj          # 项目文件

FluentDesignWPF.Demo/               # 示例应用
├── App.xaml                        # 应用程序入口
├── MainWindow.xaml                 # 主窗口
├── Pages/                          # 演示页面
│   ├── TypographyPage.xaml         # 字体系统展示页面
│   └── TextBlockPage.xaml          # FluentTextBlock 控件展示页面
└── FluentDesignWPF.Demo.csproj     # 示例项目文件

Docs/                               # 文档目录
├── Typography.md                   # 字体系统文档
└── Controls/                       # 控件文档
    └── TextBlock.md                # FluentTextBlock 控件文档
```

## 🎯 示例应用

运行 `FluentDesignWPF.Demo` 项目查看完整的色彩系统演示：

```bash
cd FluentDesignWPF
dotnet run --project FluentDesignWPF.Demo
```

示例应用包含：
- 完整的色彩系统展示
- 完整的字体系统展示
- FluentTextBlock 控件展示
- 主题切换功能
- 不同控件状态演示
- 响应式布局示例

## 🔧 开发环境

- **.NET 8.0** - 目标框架
- **WPF** - UI 框架
- **Visual Studio 2022** - 推荐 IDE

## 📚 文档

### 设计系统
- [色彩系统详细文档](FluentDesignWPF/Themes/README.md)
- [字体系统详细文档](Docs/Typography.md)

### 控件文档
- [FluentTextBlock 控件](Docs/Controls/TextBlock.md)

### 开发指南
- [控件开发指南](Docs/Controls/) (即将推出)
- [主题定制指南](Docs/Theming/) (即将推出)

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 此仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📋 开发规范

- 遵循 [FluentSystemDesign WPF控件库开发规则](DEVELOPMENT_RULES.md)
- 每个控件必须有独立的资源字典
- 支持深色和浅色主题
- 包含完整的 XML 文档注释
- 提供使用示例和文档

## 🎨 设计原则

1. **一致性** - 控件外观和行为保持一致
2. **响应性** - 支持不同屏幕尺寸和 DPI
3. **无障碍性** - 支持屏幕阅读器等辅助技术
4. **主题化** - 完整的深色/浅色主题支持
5. **流畅性** - 遵循 Fluent Design 的动效指南

## 📄 许可证

此项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Microsoft Fluent Design System](https://www.microsoft.com/design/fluent) - 设计指导
- [WPF UI](https://github.com/lepoco/wpfui) - 参考实现
- [Material Design](https://material.io/design) - 色彩理论参考

## 📞 联系

如有问题或建议，请创建 Issue 或联系维护者。

---

⭐ 如果这个项目对您有帮助，请给它一个星标！
