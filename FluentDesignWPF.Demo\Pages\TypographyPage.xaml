<!--
    FluentSystemDesign WPF控件库 - 字体系统展示页面
    
    此页面展示了控件库中定义的所有字体样式，包括：
    1. Display 字体样式 (68px-52px)
    2. Headline 字体样式 (40px-28px)
    3. Title 字体样式 (24px-18px)
    4. Label 字体样式 (16px-12px)
    5. Body 字体样式 (16px-14px)
    6. Caption 字体样式 (12px-10px)
    7. 语义化字体样式
    8. 特殊用途字体样式
    
    用于演示字体系统的完整功能和视觉效果。
-->

<UserControl x:Class="FluentDesignWPF.Demo.Pages.TypographyPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:FluentDesignWPF.Demo.Pages"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             Background="{DynamicResource BackgroundPrimaryBrush}">

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <StackPanel Margin="32" Orientation="Vertical">
            
            <!-- 页面标题 -->
            <TextBlock Text="字体系统展示 Typography System" 
                       Style="{StaticResource Display3TextStyle}" 
                       Margin="0,0,0,32" 
                       HorizontalAlignment="Center"/>
            
            <!-- 页面说明 -->
            <TextBlock Text="FluentSystemDesign WPF控件库基于Microsoft Fluent Design System设计的完整字体层次结构，提供从Caption到Display的全套字体规格，支持深色和浅色主题。" 
                       Style="{StaticResource Body1TextStyle}" 
                       Margin="0,0,0,48" 
                       TextAlignment="Center"/>

            <!-- Display 字体样式展示 -->
            <Border Background="{DynamicResource BackgroundCardBrush}" 
                    BorderBrush="{DynamicResource BorderPrimaryBrush}" 
                    BorderThickness="1" 
                    CornerRadius="{StaticResource CornerRadiusLarge}" 
                    Padding="24" 
                    Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="Display 字体样式" 
                               Style="{StaticResource Title1TextStyle}" 
                               Margin="0,0,0,16"/>
                    <TextBlock Text="用于大标题和品牌展示，字体大小从52px到68px" 
                               Style="{StaticResource Caption1TextStyle}" 
                               Margin="0,0,0,24"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Display 1 - 品牌标题" Style="{StaticResource Display1TextStyle}"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="68px / Light" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Bottom" Margin="16,0,0,0"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Display 2 - 大标题" Style="{StaticResource Display2TextStyle}"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="60px / Light" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Bottom" Margin="16,0,0,0"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Display 3 - 中标题" Style="{StaticResource Display3TextStyle}"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="52px / Regular" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Bottom" Margin="16,0,0,0"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Headline 字体样式展示 -->
            <Border Background="{DynamicResource BackgroundCardBrush}" 
                    BorderBrush="{DynamicResource BorderPrimaryBrush}" 
                    BorderThickness="1" 
                    CornerRadius="{StaticResource CornerRadiusLarge}" 
                    Padding="24" 
                    Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="Headline 字体样式" 
                               Style="{StaticResource Title1TextStyle}" 
                               Margin="0,0,0,16"/>
                    <TextBlock Text="用于页面标题和重要标题，字体大小从28px到40px" 
                               Style="{StaticResource Caption1TextStyle}" 
                               Margin="0,0,0,24"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Headline 1 - 页面主标题" Style="{StaticResource Headline1TextStyle}"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="40px / Regular" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Bottom" Margin="16,0,0,0"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Headline 2 - 大标题" Style="{StaticResource Headline2TextStyle}"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="32px / Regular" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Bottom" Margin="16,0,0,0"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Headline 3 - 中标题" Style="{StaticResource Headline3TextStyle}"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="28px / Medium" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Bottom" Margin="16,0,0,0"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Title 字体样式展示 -->
            <Border Background="{DynamicResource BackgroundCardBrush}" 
                    BorderBrush="{DynamicResource BorderPrimaryBrush}" 
                    BorderThickness="1" 
                    CornerRadius="{StaticResource CornerRadiusLarge}" 
                    Padding="24" 
                    Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="Title 字体样式" 
                               Style="{StaticResource Title1TextStyle}" 
                               Margin="0,0,0,16"/>
                    <TextBlock Text="用于章节标题和卡片标题，字体大小从18px到24px" 
                               Style="{StaticResource Caption1TextStyle}" 
                               Margin="0,0,0,24"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Title 1 - 大章节标题" Style="{StaticResource Title1TextStyle}"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="24px / Medium" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Bottom" Margin="16,0,0,0"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Title 2 - 中章节标题" Style="{StaticResource Title2TextStyle}"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="20px / Medium" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Bottom" Margin="16,0,0,0"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Title 3 - 小章节标题" Style="{StaticResource Title3TextStyle}"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="18px / SemiBold" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Bottom" Margin="16,0,0,0"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Label 字体样式展示 -->
            <Border Background="{DynamicResource BackgroundCardBrush}" 
                    BorderBrush="{DynamicResource BorderPrimaryBrush}" 
                    BorderThickness="1" 
                    CornerRadius="{StaticResource CornerRadiusLarge}" 
                    Padding="24" 
                    Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="Label 字体样式" 
                               Style="{StaticResource Title1TextStyle}" 
                               Margin="0,0,0,16"/>
                    <TextBlock Text="用于标签和按钮文本，字体大小从12px到16px" 
                               Style="{StaticResource Caption1TextStyle}" 
                               Margin="0,0,0,24"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Label 1 - 大标签" Style="{StaticResource Label1TextStyle}"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="16px / Medium" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,0,0,0"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Label 2 - 标准标签" Style="{StaticResource Label2TextStyle}"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="14px / Medium" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,0,0,0"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Label 3 - 小标签" Style="{StaticResource Label3TextStyle}"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="12px / SemiBold" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,0,0,0"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Body 字体样式展示 -->
            <Border Background="{DynamicResource BackgroundCardBrush}" 
                    BorderBrush="{DynamicResource BorderPrimaryBrush}" 
                    BorderThickness="1" 
                    CornerRadius="{StaticResource CornerRadiusLarge}" 
                    Padding="24" 
                    Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="Body 字体样式" 
                               Style="{StaticResource Title1TextStyle}" 
                               Margin="0,0,0,16"/>
                    <TextBlock Text="用于正文内容，字体大小为14px和16px" 
                               Style="{StaticResource Caption1TextStyle}" 
                               Margin="0,0,0,24"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Body 1 - 大正文，用于重要内容和强调段落。这种字体样式适合用于需要突出显示的正文内容。" Style="{StaticResource Body1TextStyle}" TextWrapping="Wrap"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="16px / Regular" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Top" Margin="16,0,0,0"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Body 2 - 标准正文，用于一般的正文内容。这是最常用的正文字体样式，适合大部分文本内容的显示。" Style="{StaticResource Body2TextStyle}" TextWrapping="Wrap" Margin="0,16,0,0"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="14px / Regular" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Top" Margin="16,16,0,0"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Caption 字体样式展示 -->
            <Border Background="{DynamicResource BackgroundCardBrush}" 
                    BorderBrush="{DynamicResource BorderPrimaryBrush}" 
                    BorderThickness="1" 
                    CornerRadius="{StaticResource CornerRadiusLarge}" 
                    Padding="24" 
                    Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="Caption 字体样式" 
                               Style="{StaticResource Title1TextStyle}" 
                               Margin="0,0,0,16"/>
                    <TextBlock Text="用于说明文字和辅助信息，字体大小为10px和12px" 
                               Style="{StaticResource Caption1TextStyle}" 
                               Margin="0,0,0,24"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Caption 1 - 标准说明文字，用于提供额外的信息和说明。" Style="{StaticResource Caption1TextStyle}"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="12px / Regular" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,0,0,0"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Caption 2 - 小说明文字，用于版权信息、时间戳等。" Style="{StaticResource Caption2TextStyle}" Margin="0,8,0,0"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="10px / Regular" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,8,0,0"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 语义化字体样式展示 -->
            <Border Background="{DynamicResource BackgroundCardBrush}"
                    BorderBrush="{DynamicResource BorderPrimaryBrush}"
                    BorderThickness="1"
                    CornerRadius="{StaticResource CornerRadiusLarge}"
                    Padding="24"
                    Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="语义化字体样式"
                               Style="{StaticResource Title1TextStyle}"
                               Margin="0,0,0,16"/>
                    <TextBlock Text="根据内容语义定义的字体样式，用于不同状态和用途的文本显示"
                               Style="{StaticResource Caption1TextStyle}"
                               Margin="0,0,0,24"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="主要文本 - 用于重要内容" Style="{StaticResource PrimaryTextStyle}"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="Primary" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,0,0,0"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="次要文本 - 用于辅助信息" Style="{StaticResource SecondaryTextStyle}" Margin="0,8,0,0"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="Secondary" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,8,0,0"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="禁用文本 - 用于不可用状态" Style="{StaticResource DisabledTextStyle}" Margin="0,8,0,0"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="Disabled" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,8,0,0"/>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="提示文本 - 用于占位符和提示" Style="{StaticResource HintTextStyle}" Margin="0,8,0,0"/>
                        <TextBlock Grid.Row="3" Grid.Column="1" Text="Hint" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,8,0,0"/>

                        <TextBlock Grid.Row="4" Grid.Column="0" Text="强调文本 - 用于重点突出" Style="{StaticResource AccentTextStyle}" Margin="0,8,0,0"/>
                        <TextBlock Grid.Row="4" Grid.Column="1" Text="Accent" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,8,0,0"/>

                        <TextBlock Grid.Row="5" Grid.Column="0" Text="成功文本 - 用于成功状态" Style="{StaticResource SuccessTextStyle}" Margin="0,8,0,0"/>
                        <TextBlock Grid.Row="5" Grid.Column="1" Text="Success" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,8,0,0"/>

                        <TextBlock Grid.Row="6" Grid.Column="0" Text="警告文本 - 用于警告信息" Style="{StaticResource WarningTextStyle}" Margin="0,8,0,0"/>
                        <TextBlock Grid.Row="6" Grid.Column="1" Text="Warning" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,8,0,0"/>

                        <TextBlock Grid.Row="7" Grid.Column="0" Text="错误文本 - 用于错误状态" Style="{StaticResource ErrorTextStyle}" Margin="0,8,0,0"/>
                        <TextBlock Grid.Row="7" Grid.Column="1" Text="Error" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,8,0,0"/>

                        <TextBlock Grid.Row="8" Grid.Column="0" Text="信息文本 - 用于一般信息" Style="{StaticResource InfoTextStyle}" Margin="0,8,0,0"/>
                        <TextBlock Grid.Row="8" Grid.Column="1" Text="Info" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,8,0,0"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 特殊用途字体样式展示 -->
            <Border Background="{DynamicResource BackgroundCardBrush}"
                    BorderBrush="{DynamicResource BorderPrimaryBrush}"
                    BorderThickness="1"
                    CornerRadius="{StaticResource CornerRadiusLarge}"
                    Padding="24"
                    Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="特殊用途字体样式"
                               Style="{StaticResource Title1TextStyle}"
                               Margin="0,0,0,16"/>
                    <TextBlock Text="针对特定用途设计的字体样式，如代码显示、数字对齐、链接等"
                               Style="{StaticResource Caption1TextStyle}"
                               Margin="0,0,0,24"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="等宽字体 - function() { return 'Hello World'; }" Style="{StaticResource MonospaceTextStyle}"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="Monospace" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,0,0,0"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="数字字体 - 123,456.78" Style="{StaticResource NumericTextStyle}" Margin="0,8,0,0"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="Numeric" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,8,0,0"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="链接文本 - 点击访问更多信息" Style="{StaticResource LinkTextStyle}" Margin="0,8,0,0"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="Link" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,8,0,0"/>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="按钮文本" Style="{StaticResource ButtonTextStyle}" Margin="0,8,0,0"/>
                        <TextBlock Grid.Row="3" Grid.Column="1" Text="Button" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,8,0,0"/>

                        <TextBlock Grid.Row="4" Grid.Column="0" Text="工具提示文本 - 用于提供额外的帮助信息" Style="{StaticResource ToolTipTextStyle}" Margin="0,8,0,0"/>
                        <TextBlock Grid.Row="4" Grid.Column="1" Text="ToolTip" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,8,0,0"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 字体族展示 -->
            <Border Background="{DynamicResource BackgroundCardBrush}"
                    BorderBrush="{DynamicResource BorderPrimaryBrush}"
                    BorderThickness="1"
                    CornerRadius="{StaticResource CornerRadiusLarge}"
                    Padding="24"
                    Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="字体族展示"
                               Style="{StaticResource Title1TextStyle}"
                               Margin="0,0,0,16"/>
                    <TextBlock Text="控件库中定义的字体族，确保在不同平台上的一致性显示"
                               Style="{StaticResource Caption1TextStyle}"
                               Margin="0,0,0,24"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="主要字体族 - Segoe UI, Microsoft YaHei UI, PingFang SC, Helvetica Neue, Arial, sans-serif" FontFamily="{StaticResource PrimaryFontFamily}" Style="{StaticResource Body2TextStyle}"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="Primary" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,0,0,0"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="等宽字体族 - Consolas, SF Mono, Monaco, Inconsolata, Fira Code, Droid Sans Mono, Courier New, monospace" FontFamily="{StaticResource MonospaceFontFamily}" Style="{StaticResource Body2TextStyle}" Margin="0,8,0,0"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="Monospace" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,8,0,0"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="数字字体族 - Segoe UI, Roboto Mono, SF Mono, Consolas, monospace" FontFamily="{StaticResource NumericFontFamily}" Style="{StaticResource Body2TextStyle}" Margin="0,8,0,0"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="Numeric" Style="{StaticResource Caption1TextStyle}" VerticalAlignment="Center" Margin="16,8,0,0"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 使用指南 -->
            <Border Background="{DynamicResource BackgroundCardBrush}"
                    BorderBrush="{DynamicResource BorderPrimaryBrush}"
                    BorderThickness="1"
                    CornerRadius="{StaticResource CornerRadiusLarge}"
                    Padding="24"
                    Margin="0,0,0,32">
                <StackPanel>
                    <TextBlock Text="使用指南"
                               Style="{StaticResource Title1TextStyle}"
                               Margin="0,0,0,16"/>

                    <TextBlock Text="1. 字体层次结构"
                               Style="{StaticResource Title3TextStyle}"
                               Margin="0,0,0,8"/>
                    <TextBlock Text="• Display: 用于品牌标题和重要展示，字体大小最大，使用Light或Regular字重"
                               Style="{StaticResource Body2TextStyle}"
                               Margin="16,0,0,4"/>
                    <TextBlock Text="• Headline: 用于页面标题和重要标题，字体大小中等，使用Regular或Medium字重"
                               Style="{StaticResource Body2TextStyle}"
                               Margin="16,0,0,4"/>
                    <TextBlock Text="• Title: 用于章节标题和卡片标题，字体大小适中，使用Medium或SemiBold字重"
                               Style="{StaticResource Body2TextStyle}"
                               Margin="16,0,0,4"/>
                    <TextBlock Text="• Label: 用于标签和按钮文本，字体大小较小，使用Medium或SemiBold字重"
                               Style="{StaticResource Body2TextStyle}"
                               Margin="16,0,0,4"/>
                    <TextBlock Text="• Body: 用于正文内容，字体大小标准，使用Regular字重"
                               Style="{StaticResource Body2TextStyle}"
                               Margin="16,0,0,4"/>
                    <TextBlock Text="• Caption: 用于说明文字和辅助信息，字体大小最小，使用Regular字重"
                               Style="{StaticResource Body2TextStyle}"
                               Margin="16,0,0,16"/>

                    <TextBlock Text="2. 语义化使用"
                               Style="{StaticResource Title3TextStyle}"
                               Margin="0,0,0,8"/>
                    <TextBlock Text="• 根据内容的重要性和语义选择合适的字体样式"
                               Style="{StaticResource Body2TextStyle}"
                               Margin="16,0,0,4"/>
                    <TextBlock Text="• 使用语义化样式（Primary, Secondary, Success, Warning, Error, Info）表达不同状态"
                               Style="{StaticResource Body2TextStyle}"
                               Margin="16,0,0,4"/>
                    <TextBlock Text="• 特殊用途使用专门的字体样式（Monospace, Numeric, Link等）"
                               Style="{StaticResource Body2TextStyle}"
                               Margin="16,0,0,16"/>

                    <TextBlock Text="3. 最佳实践"
                               Style="{StaticResource Title3TextStyle}"
                               Margin="0,0,0,8"/>
                    <TextBlock Text="• 保持字体层次的一致性，避免在同一页面使用过多不同的字体样式"
                               Style="{StaticResource Body2TextStyle}"
                               Margin="16,0,0,4"/>
                    <TextBlock Text="• 确保文本在深色和浅色主题下都有良好的可读性"
                               Style="{StaticResource Body2TextStyle}"
                               Margin="16,0,0,4"/>
                    <TextBlock Text="• 使用DynamicResource引用字体样式以支持主题切换"
                               Style="{StaticResource Body2TextStyle}"
                               Margin="16,0,0,4"/>
                    <TextBlock Text="• 考虑不同DPI设置下的字体清晰度和可读性"
                               Style="{StaticResource Body2TextStyle}"
                               Margin="16,0,0,0"/>
                </StackPanel>
            </Border>

        </StackPanel>
    </ScrollViewer>
</UserControl>
