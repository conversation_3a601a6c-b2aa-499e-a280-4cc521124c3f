# FluentTextBlock 控件

FluentTextBlock 是基于 Microsoft Fluent Design System 的现代化文本显示控件，继承自 WPF 的 TextBlock 控件，提供了丰富的样式变体和主题支持。

## 概述

FluentTextBlock 控件提供以下核心特性：

- **完整的 Typography 层次结构**：支持从 Display 到 Caption 的全套字体规格
- **语义化颜色变体**：提供 Primary、Secondary、Success、Warning、Error、Info 等语义颜色
- **多种字重支持**：支持 Light、Regular、Medium、SemiBold、Bold 五种字重
- **深色/浅色主题自动适配**：根据当前主题自动调整颜色
- **WCAG 可访问性合规**：确保文本对比度符合无障碍标准
- **响应式设计支持**：在不同 DPI 和屏幕尺寸下保持清晰度

## 属性

### TypographyVariant

定义文本的字体层次结构。

**类型**：`TypographyVariant` 枚举  
**默认值**：`Body2`

**可选值**：
- `Display1` - 最大显示字体（68px），用于品牌标题
- `Display2` - 大显示字体（60px），用于大标题展示
- `Display3` - 中显示字体（52px），用于中等标题展示
- `Headline1` - 最大标题（40px），用于页面主标题
- `Headline2` - 大标题（32px），用于重要标题
- `Headline3` - 中标题（28px），用于章节标题
- `Title1` - 大章节标题（24px），用于主要章节
- `Title2` - 中章节标题（20px），用于次要章节
- `Title3` - 小章节标题（18px），用于小节标题
- `Label1` - 大标签（16px），用于重要标签和按钮
- `Label2` - 标准标签（14px），用于一般标签
- `Label3` - 小标签（12px），用于小标签
- `Body1` - 大正文（16px），用于重要内容
- `Body2` - 标准正文（14px），用于一般内容
- `Caption1` - 标准说明文字（12px），用于说明和辅助信息
- `Caption2` - 小说明文字（10px），用于最小的说明文字

### ColorVariant

定义文本的语义化颜色。

**类型**：`ColorVariant` 枚举  
**默认值**：`Primary`

**可选值**：
- `Primary` - 主要文本颜色
- `Secondary` - 次要文本颜色，用于辅助信息
- `Disabled` - 禁用文本颜色，用于不可用状态
- `Hint` - 提示文本颜色，用于占位符和提示信息
- `Success` - 成功文本颜色，用于成功状态和正面信息
- `Warning` - 警告文本颜色，用于警告状态和注意信息
- `Error` - 错误文本颜色，用于错误状态和负面信息
- `Info` - 信息文本颜色，用于一般信息提示
- `Accent` - 强调文本颜色，用于重要信息和链接

### FontWeightVariant

定义文本的字体粗细。

**类型**：`FontWeightVariant` 枚举  
**默认值**：`Regular`

**可选值**：
- `Light` - 细体（300），用于大标题和优雅显示
- `Regular` - 常规体（400），用于正文内容
- `Medium` - 中等体（500），用于标签和重要文本
- `SemiBold` - 半粗体（600），用于小标题和强调
- `Bold` - 粗体（700），用于重要标题和强调

### IsThemeAware

控制是否启用自动主题适配。

**类型**：`bool`  
**默认值**：`true`

当设置为 `true` 时，控件会根据当前主题（深色/浅色）自动调整颜色。设置为 `false` 时，将使用固定的颜色值。

## 样式变体

### Typography 样式

FluentTextBlock 提供了完整的 Typography 层次结构样式：

```xml
<!-- Display 样式 -->
<controls:FluentTextBlock Text="Display 1" Style="{StaticResource FluentTextBlockDisplay1}"/>
<controls:FluentTextBlock Text="Display 2" Style="{StaticResource FluentTextBlockDisplay2}"/>
<controls:FluentTextBlock Text="Display 3" Style="{StaticResource FluentTextBlockDisplay3}"/>

<!-- Headline 样式 -->
<controls:FluentTextBlock Text="Headline 1" Style="{StaticResource FluentTextBlockHeadline1}"/>
<controls:FluentTextBlock Text="Headline 2" Style="{StaticResource FluentTextBlockHeadline2}"/>
<controls:FluentTextBlock Text="Headline 3" Style="{StaticResource FluentTextBlockHeadline3}"/>

<!-- Title 样式 -->
<controls:FluentTextBlock Text="Title 1" Style="{StaticResource FluentTextBlockTitle1}"/>
<controls:FluentTextBlock Text="Title 2" Style="{StaticResource FluentTextBlockTitle2}"/>
<controls:FluentTextBlock Text="Title 3" Style="{StaticResource FluentTextBlockTitle3}"/>

<!-- Label 样式 -->
<controls:FluentTextBlock Text="Label 1" Style="{StaticResource FluentTextBlockLabel1}"/>
<controls:FluentTextBlock Text="Label 2" Style="{StaticResource FluentTextBlockLabel2}"/>
<controls:FluentTextBlock Text="Label 3" Style="{StaticResource FluentTextBlockLabel3}"/>

<!-- Body 样式 -->
<controls:FluentTextBlock Text="Body 1" Style="{StaticResource FluentTextBlockBody1}"/>
<controls:FluentTextBlock Text="Body 2" Style="{StaticResource FluentTextBlockBody2}"/>

<!-- Caption 样式 -->
<controls:FluentTextBlock Text="Caption 1" Style="{StaticResource FluentTextBlockCaption1}"/>
<controls:FluentTextBlock Text="Caption 2" Style="{StaticResource FluentTextBlockCaption2}"/>
```

### 颜色样式

提供语义化的颜色变体样式：

```xml
<!-- 基础颜色 -->
<controls:FluentTextBlock Text="Primary" Style="{StaticResource FluentTextBlockPrimary}"/>
<controls:FluentTextBlock Text="Secondary" Style="{StaticResource FluentTextBlockSecondary}"/>
<controls:FluentTextBlock Text="Disabled" Style="{StaticResource FluentTextBlockDisabled}"/>
<controls:FluentTextBlock Text="Hint" Style="{StaticResource FluentTextBlockHint}"/>

<!-- 语义颜色 -->
<controls:FluentTextBlock Text="Success" Style="{StaticResource FluentTextBlockSuccess}"/>
<controls:FluentTextBlock Text="Warning" Style="{StaticResource FluentTextBlockWarning}"/>
<controls:FluentTextBlock Text="Error" Style="{StaticResource FluentTextBlockError}"/>
<controls:FluentTextBlock Text="Info" Style="{StaticResource FluentTextBlockInfo}"/>
<controls:FluentTextBlock Text="Accent" Style="{StaticResource FluentTextBlockAccent}"/>
```

### 字重样式

提供不同字体粗细的样式：

```xml
<controls:FluentTextBlock Text="Light" Style="{StaticResource FluentTextBlockLight}"/>
<controls:FluentTextBlock Text="Regular" Style="{StaticResource FluentTextBlockRegular}"/>
<controls:FluentTextBlock Text="Medium" Style="{StaticResource FluentTextBlockMedium}"/>
<controls:FluentTextBlock Text="SemiBold" Style="{StaticResource FluentTextBlockSemiBold}"/>
<controls:FluentTextBlock Text="Bold" Style="{StaticResource FluentTextBlockBold}"/>
```

## 基本使用

### 简单文本显示

```xml
<controls:FluentTextBlock Text="这是一个简单的文本"/>
```

### 使用属性设置样式

```xml
<controls:FluentTextBlock Text="标题文本"
                          TypographyVariant="Title1"
                          ColorVariant="Primary"
                          FontWeightVariant="Medium"/>
```

### 使用预定义样式

```xml
<controls:FluentTextBlock Text="成功消息"
                          Style="{StaticResource FluentTextBlockSuccess}"/>
```

## 高级用法

### 组合样式

可以组合使用不同的属性来创建自定义样式：

```xml
<controls:FluentTextBlock Text="重要警告信息"
                          TypographyVariant="Title2"
                          ColorVariant="Warning"
                          FontWeightVariant="SemiBold"/>
```

### 主题感知

控件默认启用主题感知，会根据当前主题自动调整颜色：

```xml
<!-- 这个文本会根据主题自动调整颜色 -->
<controls:FluentTextBlock Text="主题感知文本"
                          IsThemeAware="True"/>

<!-- 这个文本使用固定颜色，不受主题影响 -->
<controls:FluentTextBlock Text="固定颜色文本"
                          IsThemeAware="False"
                          Foreground="Red"/>
```

## 特殊用途样式

### 等宽字体

用于代码显示：

```xml
<controls:FluentTextBlock Text="var result = Calculate();"
                          Style="{StaticResource FluentTextBlockMonospace}"/>
```

### 数字字体

用于数字显示，确保等宽对齐：

```xml
<controls:FluentTextBlock Text="1,234.56"
                          Style="{StaticResource FluentTextBlockNumeric}"/>
```

## 使用限制

1. **继承限制**：FluentTextBlock 继承自 TextBlock，因此具有 TextBlock 的所有限制
2. **样式优先级**：当同时设置 Style 和属性时，Style 中的设置可能会覆盖属性设置
3. **主题依赖**：某些颜色变体依赖于主题系统，确保已正确初始化主题管理器

## 与其他控件的交互

FluentTextBlock 可以与其他 FluentSystemDesign 控件无缝配合使用：

- 与 FluentButton 配合使用时，确保文本颜色与按钮样式协调
- 在 FluentCard 中使用时，利用主题感知特性自动适配背景色
- 与表单控件配合时，使用适当的 ColorVariant 来表示验证状态

## 辅助功能支持

FluentTextBlock 完全支持 WPF 的辅助功能特性：

- **屏幕阅读器支持**：继承 TextBlock 的所有辅助功能特性
- **高对比度模式**：在高对比度模式下自动调整颜色
- **键盘导航**：支持标准的键盘导航模式
- **WCAG 合规**：所有颜色组合都符合 WCAG AA 级对比度要求

## 性能考虑

- **资源缓存**：样式和画刷资源会被自动缓存，提高性能
- **主题切换**：使用 DynamicResource 确保主题切换时的流畅性
- **文本渲染**：启用 ClearType 和优化的文本渲染模式

---

[返回主文档](../README.md) | [查看更多控件](./)
