using System.Windows.Controls;

namespace FluentDesignWPF.Demo.Pages
{
    /// <summary>
    /// TextBlockPage.xaml 的交互逻辑
    /// FluentTextBlock 控件展示页面，演示所有Typography层次和颜色变体
    /// </summary>
    public partial class TextBlockPage : UserControl
    {
        /// <summary>
        /// 初始化 TextBlockPage 类的新实例
        /// </summary>
        public TextBlockPage()
        {
            InitializeComponent();
        }
    }
}
