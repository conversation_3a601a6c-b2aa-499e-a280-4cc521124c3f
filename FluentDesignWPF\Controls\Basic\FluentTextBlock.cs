using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace FluentDesignWPF.Controls.Basic
{
    /// <summary>
    /// FluentTextBlock 控件，基于 Microsoft Fluent Design System 的文本显示控件
    /// 继承自 TextBlock，提供了丰富的样式变体和主题支持
    /// </summary>
    /// <remarks>
    /// FluentTextBlock 提供以下特性：
    /// 1. 完整的 Typography 层次结构支持（Display、Headline、Title、Body、Caption）
    /// 2. 语义化颜色变体（Primary、Secondary、Success、Warning、Error、Info）
    /// 3. 多种字重支持（Light、Regular、Medium、SemiBold、Bold）
    /// 4. 深色/浅色主题自动适配
    /// 5. WCAG 可访问性合规
    /// 6. 响应式设计支持
    /// </remarks>
    public class FluentTextBlock : TextBlock
    {
        #region 静态构造函数

        /// <summary>
        /// 静态构造函数，注册默认样式
        /// </summary>
        static FluentTextBlock()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(FluentTextBlock), 
                new FrameworkPropertyMetadata(typeof(FluentTextBlock)));
        }

        #endregion

        #region 依赖属性

        /// <summary>
        /// Typography 变体依赖属性
        /// 定义文本的字体层次结构，如 Display、Headline、Title、Body、Caption
        /// </summary>
        public static readonly DependencyProperty TypographyVariantProperty =
            DependencyProperty.Register(
                nameof(TypographyVariant),
                typeof(TypographyVariant),
                typeof(FluentTextBlock),
                new FrameworkPropertyMetadata(
                    TypographyVariant.Body2,
                    FrameworkPropertyMetadataOptions.AffectsRender | FrameworkPropertyMetadataOptions.AffectsMeasure,
                    OnTypographyVariantChanged));

        /// <summary>
        /// 获取或设置 Typography 变体
        /// </summary>
        /// <value>Typography 变体枚举值，默认为 Body2</value>
        public TypographyVariant TypographyVariant
        {
            get => (TypographyVariant)GetValue(TypographyVariantProperty);
            set => SetValue(TypographyVariantProperty, value);
        }

        /// <summary>
        /// 颜色变体依赖属性
        /// 定义文本的语义化颜色，如 Primary、Secondary、Success、Warning、Error、Info
        /// </summary>
        public static readonly DependencyProperty ColorVariantProperty =
            DependencyProperty.Register(
                nameof(ColorVariant),
                typeof(ColorVariant),
                typeof(FluentTextBlock),
                new FrameworkPropertyMetadata(
                    ColorVariant.Primary,
                    FrameworkPropertyMetadataOptions.AffectsRender,
                    OnColorVariantChanged));

        /// <summary>
        /// 获取或设置颜色变体
        /// </summary>
        /// <value>颜色变体枚举值，默认为 Primary</value>
        public ColorVariant ColorVariant
        {
            get => (ColorVariant)GetValue(ColorVariantProperty);
            set => SetValue(ColorVariantProperty, value);
        }

        /// <summary>
        /// 字重变体依赖属性
        /// 定义文本的字体粗细，如 Light、Regular、Medium、SemiBold、Bold
        /// </summary>
        public static readonly DependencyProperty FontWeightVariantProperty =
            DependencyProperty.Register(
                nameof(FontWeightVariant),
                typeof(FontWeightVariant),
                typeof(FluentTextBlock),
                new FrameworkPropertyMetadata(
                    FontWeightVariant.Regular,
                    FrameworkPropertyMetadataOptions.AffectsRender | FrameworkPropertyMetadataOptions.AffectsMeasure,
                    OnFontWeightVariantChanged));

        /// <summary>
        /// 获取或设置字重变体
        /// </summary>
        /// <value>字重变体枚举值，默认为 Regular</value>
        public FontWeightVariant FontWeightVariant
        {
            get => (FontWeightVariant)GetValue(FontWeightVariantProperty);
            set => SetValue(FontWeightVariantProperty, value);
        }

        /// <summary>
        /// 是否启用自动主题适配依赖属性
        /// 当启用时，控件会根据当前主题自动调整颜色
        /// </summary>
        public static readonly DependencyProperty IsThemeAwareProperty =
            DependencyProperty.Register(
                nameof(IsThemeAware),
                typeof(bool),
                typeof(FluentTextBlock),
                new FrameworkPropertyMetadata(
                    true,
                    FrameworkPropertyMetadataOptions.AffectsRender,
                    OnIsThemeAwareChanged));

        /// <summary>
        /// 获取或设置是否启用自动主题适配
        /// </summary>
        /// <value>布尔值，默认为 true</value>
        public bool IsThemeAware
        {
            get => (bool)GetValue(IsThemeAwareProperty);
            set => SetValue(IsThemeAwareProperty, value);
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 FluentTextBlock 类的新实例
        /// </summary>
        public FluentTextBlock()
        {
            // 设置默认属性
            UseLayoutRounding = true;
            TextOptions.SetTextFormattingMode(this, TextFormattingMode.Display);
            TextOptions.SetTextRenderingMode(this, TextRenderingMode.ClearType);
            
            // 应用初始样式
            ApplyTypographyVariant();
            ApplyColorVariant();
            ApplyFontWeightVariant();
        }

        #endregion

        #region 属性变更处理

        /// <summary>
        /// Typography 变体属性变更处理
        /// </summary>
        /// <param name="d">依赖对象</param>
        /// <param name="e">属性变更事件参数</param>
        private static void OnTypographyVariantChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FluentTextBlock textBlock)
            {
                textBlock.ApplyTypographyVariant();
            }
        }

        /// <summary>
        /// 颜色变体属性变更处理
        /// </summary>
        /// <param name="d">依赖对象</param>
        /// <param name="e">属性变更事件参数</param>
        private static void OnColorVariantChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FluentTextBlock textBlock)
            {
                textBlock.ApplyColorVariant();
            }
        }

        /// <summary>
        /// 字重变体属性变更处理
        /// </summary>
        /// <param name="d">依赖对象</param>
        /// <param name="e">属性变更事件参数</param>
        private static void OnFontWeightVariantChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FluentTextBlock textBlock)
            {
                textBlock.ApplyFontWeightVariant();
            }
        }

        /// <summary>
        /// 主题感知属性变更处理
        /// </summary>
        /// <param name="d">依赖对象</param>
        /// <param name="e">属性变更事件参数</param>
        private static void OnIsThemeAwareChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FluentTextBlock textBlock)
            {
                textBlock.ApplyColorVariant();
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 应用 Typography 变体样式
        /// </summary>
        private void ApplyTypographyVariant()
        {
            var resourceKey = GetTypographyResourceKey(TypographyVariant);
            if (TryFindResource(resourceKey) is Style style)
            {
                Style = style;
            }
        }

        /// <summary>
        /// 应用颜色变体样式
        /// </summary>
        private void ApplyColorVariant()
        {
            if (!IsThemeAware) return;

            var brushKey = GetColorResourceKey(ColorVariant);
            if (TryFindResource(brushKey) is Brush brush)
            {
                Foreground = brush;
            }
        }

        /// <summary>
        /// 应用字重变体样式
        /// </summary>
        private void ApplyFontWeightVariant()
        {
            var weightKey = GetFontWeightResourceKey(FontWeightVariant);
            if (TryFindResource(weightKey) is FontWeight weight)
            {
                FontWeight = weight;
            }
        }

        /// <summary>
        /// 获取 Typography 变体对应的资源键
        /// </summary>
        /// <param name="variant">Typography 变体</param>
        /// <returns>资源键字符串</returns>
        private static string GetTypographyResourceKey(TypographyVariant variant)
        {
            return variant switch
            {
                TypographyVariant.Display1 => "Display1TextStyle",
                TypographyVariant.Display2 => "Display2TextStyle",
                TypographyVariant.Display3 => "Display3TextStyle",
                TypographyVariant.Headline1 => "Headline1TextStyle",
                TypographyVariant.Headline2 => "Headline2TextStyle",
                TypographyVariant.Headline3 => "Headline3TextStyle",
                TypographyVariant.Title1 => "Title1TextStyle",
                TypographyVariant.Title2 => "Title2TextStyle",
                TypographyVariant.Title3 => "Title3TextStyle",
                TypographyVariant.Label1 => "Label1TextStyle",
                TypographyVariant.Label2 => "Label2TextStyle",
                TypographyVariant.Label3 => "Label3TextStyle",
                TypographyVariant.Body1 => "Body1TextStyle",
                TypographyVariant.Body2 => "Body2TextStyle",
                TypographyVariant.Caption1 => "Caption1TextStyle",
                TypographyVariant.Caption2 => "Caption2TextStyle",
                _ => "Body2TextStyle"
            };
        }

        /// <summary>
        /// 获取颜色变体对应的资源键
        /// </summary>
        /// <param name="variant">颜色变体</param>
        /// <returns>资源键字符串</returns>
        private static string GetColorResourceKey(ColorVariant variant)
        {
            return variant switch
            {
                ColorVariant.Primary => "ThemeTextPrimaryBrush",
                ColorVariant.Secondary => "ThemeTextSecondaryBrush",
                ColorVariant.Disabled => "ThemeTextDisabledBrush",
                ColorVariant.Hint => "ThemeTextHintBrush",
                ColorVariant.Success => "SuccessTextBrush",
                ColorVariant.Warning => "WarningTextBrush",
                ColorVariant.Error => "ErrorTextBrush",
                ColorVariant.Info => "InfoTextBrush",
                ColorVariant.Accent => "AccentTextBrush",
                _ => "ThemeTextPrimaryBrush"
            };
        }

        /// <summary>
        /// 获取字重变体对应的资源键
        /// </summary>
        /// <param name="variant">字重变体</param>
        /// <returns>资源键字符串</returns>
        private static string GetFontWeightResourceKey(FontWeightVariant variant)
        {
            return variant switch
            {
                FontWeightVariant.Light => "FontWeightLight",
                FontWeightVariant.Regular => "FontWeightRegular",
                FontWeightVariant.Medium => "FontWeightMedium",
                FontWeightVariant.SemiBold => "FontWeightSemiBold",
                FontWeightVariant.Bold => "FontWeightBold",
                _ => "FontWeightRegular"
            };
        }

        #endregion
    }
}
