<!--
    FluentSystemDesign WPF控件库 - TextBlock控件展示页面
    
    此页面展示了FluentTextBlock控件的所有功能，包括：
    1. Typography层次结构展示（Display、Headline、Title、Body、Caption）
    2. 颜色变体展示（Primary、Secondary、Success、Warning、Error、Info）
    3. 字重变体展示（Light、Regular、Medium、SemiBold、Bold）
    4. 组合样式展示
    5. 特殊用途样式展示
    6. 实时主题切换演示
    
    用于演示TextBlock控件的完整功能和视觉效果。
-->

<UserControl x:Class="FluentDesignWPF.Demo.Pages.TextBlockPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:controls="clr-namespace:FluentDesignWPF.Controls.Basic;assembly=FluentDesignWPF"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             Style="{StaticResource FluentUserControl}">

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <StackPanel Margin="32">
            
            <!-- 页面标题 -->
            <TextBlock Text="FluentTextBlock 控件展示"
                       Style="{StaticResource Headline1TextStyle}"
                       Margin="0,0,0,16"/>

            <TextBlock Text="基于Microsoft Fluent Design System的现代化文本显示控件，提供完整的Typography层次结构和语义化颜色支持。"
                       Style="{StaticResource Body1TextStyle}"
                       Margin="0,0,0,32"/>

            <!-- Typography 层次结构展示 -->
            <Border Background="{DynamicResource ThemeBackgroundCardBrush}"
                    BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                    BorderThickness="1"
                    CornerRadius="{StaticResource CornerRadiusNormal}"
                    Padding="{StaticResource PaddingLarge}"
                    Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="Typography 层次结构"
                               Style="{StaticResource Title1TextStyle}"
                               Margin="0,0,0,16"/>

                    <!-- Display 样式 -->
                    <TextBlock Text="Display 样式"
                               Style="{StaticResource Title2TextStyle}"
                               Margin="0,0,0,12"/>
                    
                    <controls:FluentTextBlock Text="Display 1 - 品牌标题展示"
                                              Style="{StaticResource FluentTextBlockDisplay1}"
                                              Margin="0,0,0,8"/>
                    
                    <controls:FluentTextBlock Text="Display 2 - 大标题展示"
                                              Style="{StaticResource FluentTextBlockDisplay2}"
                                              Margin="0,0,0,8"/>
                    
                    <controls:FluentTextBlock Text="Display 3 - 中等标题展示"
                                              Style="{StaticResource FluentTextBlockDisplay3}"
                                              Margin="0,0,0,16"/>

                    <!-- Headline 样式 -->
                    <TextBlock Text="Headline 样式"
                               Style="{StaticResource Title2TextStyle}"
                               Margin="0,0,0,12"/>
                    
                    <controls:FluentTextBlock Text="Headline 1 - 页面主标题"
                                              Style="{StaticResource FluentTextBlockHeadline1}"
                                              Margin="0,0,0,8"/>
                    
                    <controls:FluentTextBlock Text="Headline 2 - 重要标题"
                                              Style="{StaticResource FluentTextBlockHeadline2}"
                                              Margin="0,0,0,8"/>
                    
                    <controls:FluentTextBlock Text="Headline 3 - 章节标题"
                                              Style="{StaticResource FluentTextBlockHeadline3}"
                                              Margin="0,0,0,16"/>

                    <!-- Title 样式 -->
                    <TextBlock Text="Title 样式"
                               Style="{StaticResource Title2TextStyle}"
                               Margin="0,0,0,12"/>
                    
                    <controls:FluentTextBlock Text="Title 1 - 大章节标题"
                                              Style="{StaticResource FluentTextBlockTitle1}"
                                              Margin="0,0,0,8"/>
                    
                    <controls:FluentTextBlock Text="Title 2 - 中章节标题"
                                              Style="{StaticResource FluentTextBlockTitle2}"
                                              Margin="0,0,0,8"/>
                    
                    <controls:FluentTextBlock Text="Title 3 - 小章节标题"
                                              Style="{StaticResource FluentTextBlockTitle3}"
                                              Margin="0,0,0,16"/>

                    <!-- Label 样式 -->
                    <TextBlock Text="Label 样式"
                               Style="{StaticResource Title2TextStyle}"
                               Margin="0,0,0,12"/>
                    
                    <controls:FluentTextBlock Text="Label 1 - 重要标签"
                                              Style="{StaticResource FluentTextBlockLabel1}"
                                              Margin="0,0,0,8"/>
                    
                    <controls:FluentTextBlock Text="Label 2 - 标准标签"
                                              Style="{StaticResource FluentTextBlockLabel2}"
                                              Margin="0,0,0,8"/>
                    
                    <controls:FluentTextBlock Text="Label 3 - 小标签"
                                              Style="{StaticResource FluentTextBlockLabel3}"
                                              Margin="0,0,0,16"/>

                    <!-- Body 样式 -->
                    <TextBlock Text="Body 样式"
                               Style="{StaticResource Title2TextStyle}"
                               Margin="0,0,0,12"/>
                    
                    <controls:FluentTextBlock Text="Body 1 - 重要正文内容，用于需要突出显示的文本段落。"
                                              Style="{StaticResource FluentTextBlockBody1}"
                                              Margin="0,0,0,8"/>
                    
                    <controls:FluentTextBlock Text="Body 2 - 标准正文内容，用于一般的文本段落和描述信息。这是默认的正文样式。"
                                              Style="{StaticResource FluentTextBlockBody2}"
                                              Margin="0,0,0,16"/>

                    <!-- Caption 样式 -->
                    <TextBlock Text="Caption 样式"
                               Style="{StaticResource Title2TextStyle}"
                               Margin="0,0,0,12"/>
                    
                    <controls:FluentTextBlock Text="Caption 1 - 标准说明文字，用于提供额外的说明和辅助信息。"
                                              Style="{StaticResource FluentTextBlockCaption1}"
                                              Margin="0,0,0,8"/>
                    
                    <controls:FluentTextBlock Text="Caption 2 - 小说明文字，用于最小的说明和版权信息。"
                                              Style="{StaticResource FluentTextBlockCaption2}"
                                              Margin="0,0,0,8"/>
                </StackPanel>
            </Border>

            <!-- 颜色变体展示 -->
            <Border Background="{DynamicResource ThemeBackgroundCardBrush}"
                    BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                    BorderThickness="1"
                    CornerRadius="{StaticResource CornerRadiusNormal}"
                    Padding="{StaticResource PaddingLarge}"
                    Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="颜色变体"
                               Style="{StaticResource Title1TextStyle}"
                               Margin="0,0,0,16"/>

                    <TextBlock Text="语义化颜色变体，用于不同的信息类型和状态表示。"
                               Style="{StaticResource Body2TextStyle}"
                               Margin="0,0,0,16"/>

                    <!-- 基础颜色变体 -->
                    <TextBlock Text="基础颜色"
                               Style="{StaticResource Title2TextStyle}"
                               Margin="0,0,0,12"/>

                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                        <StackPanel Margin="0,0,24,0">
                            <controls:FluentTextBlock Text="Primary"
                                                      Style="{StaticResource FluentTextBlockPrimary}"
                                                      Margin="0,0,0,4"/>
                            <controls:FluentTextBlock Text="主要文本颜色"
                                                      Style="{StaticResource FluentTextBlockCaption1}"
                                                      ColorVariant="Secondary"/>
                        </StackPanel>

                        <StackPanel Margin="0,0,24,0">
                            <controls:FluentTextBlock Text="Secondary"
                                                      Style="{StaticResource FluentTextBlockSecondary}"
                                                      Margin="0,0,0,4"/>
                            <controls:FluentTextBlock Text="次要文本颜色"
                                                      Style="{StaticResource FluentTextBlockCaption1}"
                                                      ColorVariant="Secondary"/>
                        </StackPanel>

                        <StackPanel Margin="0,0,24,0">
                            <controls:FluentTextBlock Text="Disabled"
                                                      Style="{StaticResource FluentTextBlockDisabled}"
                                                      Margin="0,0,0,4"/>
                            <controls:FluentTextBlock Text="禁用文本颜色"
                                                      Style="{StaticResource FluentTextBlockCaption1}"
                                                      ColorVariant="Secondary"/>
                        </StackPanel>

                        <StackPanel Margin="0,0,24,0">
                            <controls:FluentTextBlock Text="Hint"
                                                      Style="{StaticResource FluentTextBlockHint}"
                                                      Margin="0,0,0,4"/>
                            <controls:FluentTextBlock Text="提示文本颜色"
                                                      Style="{StaticResource FluentTextBlockCaption1}"
                                                      ColorVariant="Secondary"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- 语义颜色变体 -->
                    <TextBlock Text="语义颜色"
                               Style="{StaticResource Title2TextStyle}"
                               Margin="0,0,0,12"/>

                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                        <StackPanel Margin="0,0,24,0">
                            <controls:FluentTextBlock Text="Success"
                                                      Style="{StaticResource FluentTextBlockSuccess}"
                                                      Margin="0,0,0,4"/>
                            <controls:FluentTextBlock Text="成功状态"
                                                      Style="{StaticResource FluentTextBlockCaption1}"
                                                      ColorVariant="Secondary"/>
                        </StackPanel>

                        <StackPanel Margin="0,0,24,0">
                            <controls:FluentTextBlock Text="Warning"
                                                      Style="{StaticResource FluentTextBlockWarning}"
                                                      Margin="0,0,0,4"/>
                            <controls:FluentTextBlock Text="警告状态"
                                                      Style="{StaticResource FluentTextBlockCaption1}"
                                                      ColorVariant="Secondary"/>
                        </StackPanel>

                        <StackPanel Margin="0,0,24,0">
                            <controls:FluentTextBlock Text="Error"
                                                      Style="{StaticResource FluentTextBlockError}"
                                                      Margin="0,0,0,4"/>
                            <controls:FluentTextBlock Text="错误状态"
                                                      Style="{StaticResource FluentTextBlockCaption1}"
                                                      ColorVariant="Secondary"/>
                        </StackPanel>

                        <StackPanel Margin="0,0,24,0">
                            <controls:FluentTextBlock Text="Info"
                                                      Style="{StaticResource FluentTextBlockInfo}"
                                                      Margin="0,0,0,4"/>
                            <controls:FluentTextBlock Text="信息状态"
                                                      Style="{StaticResource FluentTextBlockCaption1}"
                                                      ColorVariant="Secondary"/>
                        </StackPanel>

                        <StackPanel Margin="0,0,24,0">
                            <controls:FluentTextBlock Text="Accent"
                                                      Style="{StaticResource FluentTextBlockAccent}"
                                                      Margin="0,0,0,4"/>
                            <controls:FluentTextBlock Text="强调颜色"
                                                      Style="{StaticResource FluentTextBlockCaption1}"
                                                      ColorVariant="Secondary"/>
                        </StackPanel>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- 字重变体展示 -->
            <Border Background="{DynamicResource ThemeBackgroundCardBrush}"
                    BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                    BorderThickness="1"
                    CornerRadius="{StaticResource CornerRadiusNormal}"
                    Padding="{StaticResource PaddingLarge}"
                    Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="字重变体"
                               Style="{StaticResource Title1TextStyle}"
                               Margin="0,0,0,16"/>

                    <TextBlock Text="不同的字体粗细级别，用于创建视觉层次和强调效果。"
                               Style="{StaticResource Body2TextStyle}"
                               Margin="0,0,0,16"/>

                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                        <StackPanel Margin="0,0,32,0">
                            <controls:FluentTextBlock Text="Light (300)"
                                                      Style="{StaticResource FluentTextBlockLight}"
                                                      FontSize="18"
                                                      Margin="0,0,0,4"/>
                            <controls:FluentTextBlock Text="细体，用于大标题"
                                                      Style="{StaticResource FluentTextBlockCaption1}"
                                                      ColorVariant="Secondary"/>
                        </StackPanel>

                        <StackPanel Margin="0,0,32,0">
                            <controls:FluentTextBlock Text="Regular (400)"
                                                      Style="{StaticResource FluentTextBlockRegular}"
                                                      FontSize="18"
                                                      Margin="0,0,0,4"/>
                            <controls:FluentTextBlock Text="常规体，默认字重"
                                                      Style="{StaticResource FluentTextBlockCaption1}"
                                                      ColorVariant="Secondary"/>
                        </StackPanel>

                        <StackPanel Margin="0,0,32,0">
                            <controls:FluentTextBlock Text="Medium (500)"
                                                      Style="{StaticResource FluentTextBlockMedium}"
                                                      FontSize="18"
                                                      Margin="0,0,0,4"/>
                            <controls:FluentTextBlock Text="中等体，用于标签"
                                                      Style="{StaticResource FluentTextBlockCaption1}"
                                                      ColorVariant="Secondary"/>
                        </StackPanel>

                        <StackPanel Margin="0,0,32,0">
                            <controls:FluentTextBlock Text="SemiBold (600)"
                                                      Style="{StaticResource FluentTextBlockSemiBold}"
                                                      FontSize="18"
                                                      Margin="0,0,0,4"/>
                            <controls:FluentTextBlock Text="半粗体，用于强调"
                                                      Style="{StaticResource FluentTextBlockCaption1}"
                                                      ColorVariant="Secondary"/>
                        </StackPanel>

                        <StackPanel Margin="0,0,32,0">
                            <controls:FluentTextBlock Text="Bold (700)"
                                                      Style="{StaticResource FluentTextBlockBold}"
                                                      FontSize="18"
                                                      Margin="0,0,0,4"/>
                            <controls:FluentTextBlock Text="粗体，用于重要标题"
                                                      Style="{StaticResource FluentTextBlockCaption1}"
                                                      ColorVariant="Secondary"/>
                        </StackPanel>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- 主题切换测试 -->
            <Border Background="{DynamicResource ThemeBackgroundCardBrush}"
                    BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                    BorderThickness="1"
                    CornerRadius="{StaticResource CornerRadiusNormal}"
                    Padding="{StaticResource PaddingLarge}"
                    Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="主题切换测试"
                               Style="{StaticResource Title1TextStyle}"
                               Margin="0,0,0,16"/>

                    <TextBlock Text="以下文本使用ColorVariant属性设置颜色，应该能够正确响应主题切换。"
                               Style="{StaticResource Body2TextStyle}"
                               Margin="0,0,0,16"/>

                    <!-- 使用ColorVariant属性的测试文本 -->
                    <StackPanel Margin="0,0,0,16">
                        <controls:FluentTextBlock Text="Primary颜色 - 使用ColorVariant属性"
                                                  TypographyVariant="Body1"
                                                  ColorVariant="Primary"
                                                  Margin="0,0,0,8"/>

                        <controls:FluentTextBlock Text="Secondary颜色 - 使用ColorVariant属性"
                                                  TypographyVariant="Body1"
                                                  ColorVariant="Secondary"
                                                  Margin="0,0,0,8"/>

                        <controls:FluentTextBlock Text="Success颜色 - 使用ColorVariant属性"
                                                  TypographyVariant="Body1"
                                                  ColorVariant="Success"
                                                  Margin="0,0,0,8"/>

                        <controls:FluentTextBlock Text="Warning颜色 - 使用ColorVariant属性"
                                                  TypographyVariant="Body1"
                                                  ColorVariant="Warning"
                                                  Margin="0,0,0,8"/>

                        <controls:FluentTextBlock Text="Error颜色 - 使用ColorVariant属性"
                                                  TypographyVariant="Body1"
                                                  ColorVariant="Error"
                                                  Margin="0,0,0,8"/>

                        <controls:FluentTextBlock Text="Info颜色 - 使用ColorVariant属性"
                                                  TypographyVariant="Body1"
                                                  ColorVariant="Info"
                                                  Margin="0,0,0,8"/>

                        <controls:FluentTextBlock Text="Accent颜色 - 使用ColorVariant属性"
                                                  TypographyVariant="Body1"
                                                  ColorVariant="Accent"
                                                  Margin="0,0,0,8"/>
                    </StackPanel>

                    <TextBlock Text="请点击主窗口的主题切换按钮来测试颜色是否正确更新。"
                               Style="{StaticResource Caption1TextStyle}"
                               Foreground="{DynamicResource ThemeTextSecondaryBrush}"
                               Margin="0,0,0,8"/>
                </StackPanel>
            </Border>

        </StackPanel>
    </ScrollViewer>
</UserControl>
