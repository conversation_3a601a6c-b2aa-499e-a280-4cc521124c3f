<!--
    FluentSystemDesign WPF控件库 - 字体系统资源字典
    
    此文件定义了基于Microsoft Fluent Design System的完整字体层次结构，
    提供从Caption到Display的全套字体规格，支持深色和浅色主题。
    
    字体系统特点：
    1. 基于Microsoft Fluent Design System设计语言
    2. 支持多种字体大小（12px-68px）
    3. 定义字体粗细（Light, Regular, Medium, SemiBold, Bold）
    4. 设置合适的行高和字符间距
    5. 确保在不同DPI设置下的清晰度
    6. 符合WCAG无障碍访问标准
    
    字体层次结构：
    - Display: 68px, 60px, 52px (用于大标题和品牌展示)
    - Headline: 40px, 32px, 28px (用于页面标题和重要标题)
    - Title: 24px, 20px, 18px (用于章节标题和卡片标题)
    - Label: 16px, 14px, 12px (用于标签和按钮文本)
    - Body: 16px, 14px (用于正文内容)
    - Caption: 12px, 10px (用于说明文字和辅助信息)
    
    使用方法：
    - 在控件样式中通过StaticResource引用字体样式
    - 配合Colors.xaml和Brushes.xaml使用获得最佳效果
    - 支持主题切换时的字体颜色动态更新
-->

<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <!-- 引用Colors.xaml和Brushes.xaml资源字典 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Colors.xaml"/>
        <ResourceDictionary Source="Brushes.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- ================================ -->
    <!-- 字体族定义 (Font Families)        -->
    <!-- ================================ -->
    
    <!-- 主要字体族 - 优先使用系统字体 -->
    <FontFamily x:Key="PrimaryFontFamily">Segoe UI, Microsoft YaHei UI, PingFang SC, Helvetica Neue, Arial, sans-serif</FontFamily>
    
    <!-- 等宽字体族 - 用于代码和数据显示 -->
    <FontFamily x:Key="MonospaceFontFamily">Consolas, SF Mono, Monaco, Inconsolata, Fira Code, Droid Sans Mono, Courier New, monospace</FontFamily>
    
    <!-- 数字字体族 - 用于数字显示，确保等宽对齐 -->
    <FontFamily x:Key="NumericFontFamily">Segoe UI, Roboto Mono, SF Mono, Consolas, monospace</FontFamily>

    <!-- ================================ -->
    <!-- 字体大小定义 (Font Sizes)         -->
    <!-- ================================ -->
    
    <!-- Display 字体大小 - 用于大标题和品牌展示 -->
    <sys:Double x:Key="FontSizeDisplay1">68</sys:Double>    <!-- 最大显示字体 -->
    <sys:Double x:Key="FontSizeDisplay2">60</sys:Double>    <!-- 大显示字体 -->
    <sys:Double x:Key="FontSizeDisplay3">52</sys:Double>    <!-- 中显示字体 -->
    
    <!-- Headline 字体大小 - 用于页面标题和重要标题 -->
    <sys:Double x:Key="FontSizeHeadline1">40</sys:Double>   <!-- 最大标题 -->
    <sys:Double x:Key="FontSizeHeadline2">32</sys:Double>   <!-- 大标题 -->
    <sys:Double x:Key="FontSizeHeadline3">28</sys:Double>   <!-- 中标题 -->
    
    <!-- Title 字体大小 - 用于章节标题和卡片标题 -->
    <sys:Double x:Key="FontSizeTitle1">24</sys:Double>      <!-- 大章节标题 -->
    <sys:Double x:Key="FontSizeTitle2">20</sys:Double>      <!-- 中章节标题 -->
    <sys:Double x:Key="FontSizeTitle3">18</sys:Double>      <!-- 小章节标题 -->
    
    <!-- Label 字体大小 - 用于标签和按钮文本 -->
    <sys:Double x:Key="FontSizeLabel1">16</sys:Double>      <!-- 大标签 -->
    <sys:Double x:Key="FontSizeLabel2">14</sys:Double>      <!-- 标准标签 -->
    <sys:Double x:Key="FontSizeLabel3">12</sys:Double>      <!-- 小标签 -->
    
    <!-- Body 字体大小 - 用于正文内容 -->
    <sys:Double x:Key="FontSizeBody1">16</sys:Double>       <!-- 大正文 -->
    <sys:Double x:Key="FontSizeBody2">14</sys:Double>       <!-- 标准正文 -->
    
    <!-- Caption 字体大小 - 用于说明文字和辅助信息 -->
    <sys:Double x:Key="FontSizeCaption1">12</sys:Double>    <!-- 标准说明文字 -->
    <sys:Double x:Key="FontSizeCaption2">10</sys:Double>    <!-- 小说明文字 -->

    <!-- ================================ -->
    <!-- 字体粗细定义 (Font Weights)       -->
    <!-- ================================ -->
    
    <FontWeight x:Key="FontWeightLight">Light</FontWeight>          <!-- 300 -->
    <FontWeight x:Key="FontWeightRegular">Normal</FontWeight>       <!-- 400 -->
    <FontWeight x:Key="FontWeightMedium">Medium</FontWeight>        <!-- 500 -->
    <FontWeight x:Key="FontWeightSemiBold">SemiBold</FontWeight>    <!-- 600 -->
    <FontWeight x:Key="FontWeightBold">Bold</FontWeight>            <!-- 700 -->

    <!-- ================================ -->
    <!-- 行高定义 (Line Heights)           -->
    <!-- ================================ -->
    
    <!-- Display 行高 -->
    <sys:Double x:Key="LineHeightDisplay1">76</sys:Double>      <!-- 1.12 倍行高 -->
    <sys:Double x:Key="LineHeightDisplay2">68</sys:Double>      <!-- 1.13 倍行高 -->
    <sys:Double x:Key="LineHeightDisplay3">60</sys:Double>      <!-- 1.15 倍行高 -->
    
    <!-- Headline 行高 -->
    <sys:Double x:Key="LineHeightHeadline1">48</sys:Double>     <!-- 1.2 倍行高 -->
    <sys:Double x:Key="LineHeightHeadline2">40</sys:Double>     <!-- 1.25 倍行高 -->
    <sys:Double x:Key="LineHeightHeadline3">36</sys:Double>     <!-- 1.29 倍行高 -->
    
    <!-- Title 行高 -->
    <sys:Double x:Key="LineHeightTitle1">32</sys:Double>        <!-- 1.33 倍行高 -->
    <sys:Double x:Key="LineHeightTitle2">28</sys:Double>        <!-- 1.4 倍行高 -->
    <sys:Double x:Key="LineHeightTitle3">26</sys:Double>        <!-- 1.44 倍行高 -->
    
    <!-- Label 行高 -->
    <sys:Double x:Key="LineHeightLabel1">24</sys:Double>        <!-- 1.5 倍行高 -->
    <sys:Double x:Key="LineHeightLabel2">20</sys:Double>        <!-- 1.43 倍行高 -->
    <sys:Double x:Key="LineHeightLabel3">16</sys:Double>        <!-- 1.33 倍行高 -->
    
    <!-- Body 行高 -->
    <sys:Double x:Key="LineHeightBody1">24</sys:Double>         <!-- 1.5 倍行高 -->
    <sys:Double x:Key="LineHeightBody2">20</sys:Double>         <!-- 1.43 倍行高 -->
    
    <!-- Caption 行高 -->
    <sys:Double x:Key="LineHeightCaption1">16</sys:Double>      <!-- 1.33 倍行高 -->
    <sys:Double x:Key="LineHeightCaption2">14</sys:Double>      <!-- 1.4 倍行高 -->

    <!-- ================================ -->
    <!-- 字符间距定义 (Letter Spacing)     -->
    <!-- ================================ -->
    
    <!-- Display 字符间距 -->
    <sys:Double x:Key="LetterSpacingDisplay">-0.5</sys:Double>      <!-- 紧密间距 -->
    
    <!-- Headline 字符间距 -->
    <sys:Double x:Key="LetterSpacingHeadline">-0.25</sys:Double>    <!-- 略紧间距 -->
    
    <!-- Title 字符间距 -->
    <sys:Double x:Key="LetterSpacingTitle">0</sys:Double>           <!-- 标准间距 -->
    
    <!-- Label 字符间距 -->
    <sys:Double x:Key="LetterSpacingLabel">0.1</sys:Double>         <!-- 略宽间距 -->
    
    <!-- Body 字符间距 -->
    <sys:Double x:Key="LetterSpacingBody">0</sys:Double>            <!-- 标准间距 -->
    
    <!-- Caption 字符间距 -->
    <sys:Double x:Key="LetterSpacingCaption">0.4</sys:Double>       <!-- 宽间距 -->

    <!-- ================================ -->
    <!-- 语义化字体颜色 (Semantic Colors)  -->
    <!-- ================================ -->
    
    <!-- 主要文本颜色 -->
    <SolidColorBrush x:Key="PrimaryTextBrush" Color="{DynamicResource TextPrimaryLight}"/>
    
    <!-- 次要文本颜色 -->
    <SolidColorBrush x:Key="SecondaryTextBrush" Color="{DynamicResource TextSecondaryLight}"/>
    
    <!-- 禁用文本颜色 -->
    <SolidColorBrush x:Key="DisabledTextBrush" Color="{DynamicResource TextDisabledLight}"/>
    
    <!-- 提示文本颜色 -->
    <SolidColorBrush x:Key="HintTextBrush" Color="{DynamicResource TextHintLight}"/>
    
    <!-- 强调文本颜色 -->
    <SolidColorBrush x:Key="AccentTextBrush" Color="{StaticResource PrimaryColor500}"/>
    
    <!-- 成功文本颜色 -->
    <SolidColorBrush x:Key="SuccessTextBrush" Color="{StaticResource SuccessColor500}"/>
    
    <!-- 警告文本颜色 -->
    <SolidColorBrush x:Key="WarningTextBrush" Color="{StaticResource WarningColor700}"/>
    
    <!-- 错误文本颜色 -->
    <SolidColorBrush x:Key="ErrorTextBrush" Color="{StaticResource ErrorColor500}"/>
    
    <!-- 信息文本颜色 -->
    <SolidColorBrush x:Key="InfoTextBrush" Color="{StaticResource InfoColor500}"/>

    <!-- ================================ -->
    <!-- Display 字体样式 (Display Styles) -->
    <!-- ================================ -->

    <!-- Display 1 - 最大显示字体，用于品牌标题和重要展示 -->
    <Style x:Key="Display1TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeDisplay1}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightLight}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightDisplay1}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- Display 2 - 大显示字体 -->
    <Style x:Key="Display2TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeDisplay2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightLight}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightDisplay2}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- Display 3 - 中显示字体 -->
    <Style x:Key="Display3TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeDisplay3}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightDisplay3}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- ================================ -->
    <!-- Headline 字体样式 (Headline Styles) -->
    <!-- ================================ -->

    <!-- Headline 1 - 最大标题，用于页面主标题 -->
    <Style x:Key="Headline1TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeHeadline1}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightHeadline1}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- Headline 2 - 大标题 -->
    <Style x:Key="Headline2TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeHeadline2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightHeadline2}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- Headline 3 - 中标题 -->
    <Style x:Key="Headline3TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeHeadline3}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightHeadline3}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- ================================ -->
    <!-- Title 字体样式 (Title Styles)     -->
    <!-- ================================ -->

    <!-- Title 1 - 大章节标题 -->
    <Style x:Key="Title1TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeTitle1}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightTitle1}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- Title 2 - 中章节标题 -->
    <Style x:Key="Title2TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeTitle2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightTitle2}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- Title 3 - 小章节标题 -->
    <Style x:Key="Title3TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeTitle3}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightTitle3}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- ================================ -->
    <!-- Label 字体样式 (Label Styles)     -->
    <!-- ================================ -->

    <!-- Label 1 - 大标签，用于重要标签和按钮 -->
    <Style x:Key="Label1TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeLabel1}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightLabel1}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="NoWrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- Label 2 - 标准标签 -->
    <Style x:Key="Label2TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeLabel2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightLabel2}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="NoWrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- Label 3 - 小标签 -->
    <Style x:Key="Label3TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeLabel3}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightLabel3}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="NoWrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- ================================ -->
    <!-- Body 字体样式 (Body Styles)       -->
    <!-- ================================ -->

    <!-- Body 1 - 大正文，用于重要内容 -->
    <Style x:Key="Body1TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBody1}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBody1}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- Body 2 - 标准正文 -->
    <Style x:Key="Body2TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBody2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBody2}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- ================================ -->
    <!-- Caption 字体样式 (Caption Styles) -->
    <!-- ================================ -->

    <!-- Caption 1 - 标准说明文字 -->
    <Style x:Key="Caption1TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeCaption1}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightCaption1}"/>
        <Setter Property="Foreground" Value="{StaticResource SecondaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- Caption 2 - 小说明文字 -->
    <Style x:Key="Caption2TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeCaption2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightCaption2}"/>
        <Setter Property="Foreground" Value="{StaticResource SecondaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- ================================ -->
    <!-- 语义化字体样式 (Semantic Styles)  -->
    <!-- ================================ -->

    <!-- 主要文本样式 -->
    <Style x:Key="PrimaryTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Body2TextStyle}">
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
    </Style>

    <!-- 次要文本样式 -->
    <Style x:Key="SecondaryTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Body2TextStyle}">
        <Setter Property="Foreground" Value="{StaticResource SecondaryTextBrush}"/>
    </Style>

    <!-- 禁用文本样式 -->
    <Style x:Key="DisabledTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Body2TextStyle}">
        <Setter Property="Foreground" Value="{StaticResource DisabledTextBrush}"/>
    </Style>

    <!-- 提示文本样式 -->
    <Style x:Key="HintTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Caption1TextStyle}">
        <Setter Property="Foreground" Value="{StaticResource HintTextBrush}"/>
    </Style>

    <!-- 强调文本样式 -->
    <Style x:Key="AccentTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Body2TextStyle}">
        <Setter Property="Foreground" Value="{StaticResource AccentTextBrush}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
    </Style>

    <!-- 成功文本样式 -->
    <Style x:Key="SuccessTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Body2TextStyle}">
        <Setter Property="Foreground" Value="{StaticResource SuccessTextBrush}"/>
    </Style>

    <!-- 警告文本样式 -->
    <Style x:Key="WarningTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Body2TextStyle}">
        <Setter Property="Foreground" Value="{StaticResource WarningTextBrush}"/>
    </Style>

    <!-- 错误文本样式 -->
    <Style x:Key="ErrorTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Body2TextStyle}">
        <Setter Property="Foreground" Value="{StaticResource ErrorTextBrush}"/>
    </Style>

    <!-- 信息文本样式 -->
    <Style x:Key="InfoTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Body2TextStyle}">
        <Setter Property="Foreground" Value="{StaticResource InfoTextBrush}"/>
    </Style>

    <!-- ================================ -->
    <!-- 特殊用途字体样式 (Special Styles) -->
    <!-- ================================ -->

    <!-- 等宽字体样式 - 用于代码显示 -->
    <Style x:Key="MonospaceTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource MonospaceFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBody2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBody2}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- 数字字体样式 - 用于数字显示，确保等宽对齐 -->
    <Style x:Key="NumericTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource NumericFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBody2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBody2}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="NoWrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
        <Setter Property="HorizontalAlignment" Value="Right"/>
    </Style>

    <!-- 链接文本样式 -->
    <Style x:Key="LinkTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Body2TextStyle}">
        <Setter Property="Foreground" Value="{StaticResource AccentTextBrush}"/>
        <Setter Property="TextDecorations" Value="Underline"/>
        <Setter Property="Cursor" Value="Hand"/>
    </Style>

    <!-- 按钮文本样式 -->
    <Style x:Key="ButtonTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Label2TextStyle}">
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- 工具提示文本样式 -->
    <Style x:Key="ToolTipTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Caption1TextStyle}">
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="MaxWidth" Value="300"/>
    </Style>

    <!-- ================================ -->
    <!-- 常用字体样式别名 (Common Aliases) -->
    <!-- ================================ -->

    <!-- 默认文本样式 -->
    <Style x:Key="DefaultTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Body2TextStyle}"/>

    <!-- 标题样式别名 -->
    <Style x:Key="TitleTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Title2TextStyle}"/>
    <Style x:Key="SubtitleTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Title3TextStyle}"/>

    <!-- 正文样式别名 -->
    <Style x:Key="BodyTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Body2TextStyle}"/>
    <Style x:Key="LargeBodyTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Body1TextStyle}"/>

    <!-- 说明样式别名 -->
    <Style x:Key="CaptionTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Caption1TextStyle}"/>
    <Style x:Key="SmallCaptionTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Caption2TextStyle}"/>

    <!-- 标签样式别名 -->
    <Style x:Key="LabelTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Label2TextStyle}"/>
    <Style x:Key="LargeLabelTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Label1TextStyle}"/>
    <Style x:Key="SmallLabelTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Label3TextStyle}"/>

    <!-- ================================ -->
    <!-- 控件默认字体样式 (Default Control Styles) -->
    <!-- ================================ -->

    <!-- 为常用控件设置默认字体样式 -->

    <!-- TextBlock 默认样式 -->
    <Style TargetType="TextBlock" BasedOn="{StaticResource DefaultTextStyle}"/>

    <!-- Label 默认样式 -->
    <Style TargetType="Label">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeLabel2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- Button 默认字体样式 -->
    <Style TargetType="Button">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeLabel2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- TextBox 默认字体样式 -->
    <Style TargetType="TextBox">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBody2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- ComboBox 默认字体样式 -->
    <Style TargetType="ComboBox">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBody2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

</ResourceDictionary>
