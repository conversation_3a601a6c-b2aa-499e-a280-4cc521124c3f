D:\Project\04  FluentDesignWPF\FluentDesignWPF\bin\Debug\net8.0-windows\FluentDesignWPF.deps.json
D:\Project\04  FluentDesignWPF\FluentDesignWPF\bin\Debug\net8.0-windows\FluentDesignWPF.dll
D:\Project\04  FluentDesignWPF\FluentDesignWPF\bin\Debug\net8.0-windows\FluentDesignWPF.pdb
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\Themes\Brushes.baml
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\Themes\Colors.baml
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\Themes\Generic.baml
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\Themes\ThemeManager.baml
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\FluentDesignWPF_MarkupCompile.cache
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\FluentDesignWPF.g.resources
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\FluentDesignWPF.GeneratedMSBuildEditorConfig.editorconfig
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\FluentDesignWPF.AssemblyInfoInputs.cache
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\FluentDesignWPF.AssemblyInfo.cs
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\FluentDesignWPF.csproj.CoreCompileInputs.cache
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\FluentDesignWPF.dll
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\refint\FluentDesignWPF.dll
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\FluentDesignWPF.pdb
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\ref\FluentDesignWPF.dll
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\Themes\DarkTheme.baml
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\Themes\LightTheme.baml
D:\Project\04  FluentDesignWPF\FluentDesignWPF\obj\Debug\net8.0-windows\Themes\Typography.baml
