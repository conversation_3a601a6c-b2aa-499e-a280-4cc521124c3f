<!--
    FluentSystemDesign WPF控件库 - TextBlock控件样式资源字典
    
    此文件定义了FluentTextBlock控件的所有样式变体，包括：
    1. 默认样式
    2. Typography层次结构样式（Display、Headline、Title、Body、Caption）
    3. 颜色变体样式（Primary、Secondary、Success、Warning、Error、Info）
    4. 字重变体样式（Light、Regular、Medium、SemiBold、Bold）
    5. 组合样式变体
    
    样式特点：
    - 基于Microsoft Fluent Design System设计语言
    - 支持深色和浅色主题自动切换
    - 符合WCAG可访问性标准
    - 响应式设计支持
    - 完整的视觉状态管理
-->

<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:FluentDesignWPF.Controls.Basic">

    <!-- 引用基础资源字典 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="../Colors.xaml"/>
        <ResourceDictionary Source="../Brushes.xaml"/>
        <ResourceDictionary Source="../Typography.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- ================================ -->
    <!-- FluentTextBlock 默认样式          -->
    <!-- ================================ -->
    
    <!-- FluentTextBlock 默认样式 -->
    <Style TargetType="controls:FluentTextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBody2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBody2}"/>
        <Setter Property="Foreground" Value="{DynamicResource ThemeTextPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="IsThemeAware" Value="True"/>
        <Setter Property="TypographyVariant" Value="Body2"/>
        <Setter Property="ColorVariant" Value="Primary"/>
        <Setter Property="FontWeightVariant" Value="Regular"/>
    </Style>

    <!-- ================================ -->
    <!-- Typography 变体样式               -->
    <!-- ================================ -->

    <!-- Display 样式变体 -->
    <Style x:Key="FluentTextBlockDisplay1" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeDisplay1}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightLight}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightDisplay1}"/>
        <Setter Property="TypographyVariant" Value="Display1"/>
    </Style>

    <Style x:Key="FluentTextBlockDisplay2" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeDisplay2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightLight}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightDisplay2}"/>
        <Setter Property="TypographyVariant" Value="Display2"/>
    </Style>

    <Style x:Key="FluentTextBlockDisplay3" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeDisplay3}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightDisplay3}"/>
        <Setter Property="TypographyVariant" Value="Display3"/>
    </Style>

    <!-- Headline 样式变体 -->
    <Style x:Key="FluentTextBlockHeadline1" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeHeadline1}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightHeadline1}"/>
        <Setter Property="TypographyVariant" Value="Headline1"/>
    </Style>

    <Style x:Key="FluentTextBlockHeadline2" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeHeadline2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightHeadline2}"/>
        <Setter Property="TypographyVariant" Value="Headline2"/>
    </Style>

    <Style x:Key="FluentTextBlockHeadline3" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeHeadline3}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightHeadline3}"/>
        <Setter Property="TypographyVariant" Value="Headline3"/>
    </Style>

    <!-- Title 样式变体 -->
    <Style x:Key="FluentTextBlockTitle1" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeTitle1}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightTitle1}"/>
        <Setter Property="TypographyVariant" Value="Title1"/>
    </Style>

    <Style x:Key="FluentTextBlockTitle2" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeTitle2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightTitle2}"/>
        <Setter Property="TypographyVariant" Value="Title2"/>
    </Style>

    <Style x:Key="FluentTextBlockTitle3" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeTitle3}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightTitle3}"/>
        <Setter Property="TypographyVariant" Value="Title3"/>
    </Style>

    <!-- Label 样式变体 -->
    <Style x:Key="FluentTextBlockLabel1" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeLabel1}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightLabel1}"/>
        <Setter Property="TextWrapping" Value="NoWrap"/>
        <Setter Property="TypographyVariant" Value="Label1"/>
    </Style>

    <Style x:Key="FluentTextBlockLabel2" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeLabel2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightLabel2}"/>
        <Setter Property="TextWrapping" Value="NoWrap"/>
        <Setter Property="TypographyVariant" Value="Label2"/>
    </Style>

    <Style x:Key="FluentTextBlockLabel3" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeLabel3}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightLabel3}"/>
        <Setter Property="TextWrapping" Value="NoWrap"/>
        <Setter Property="TypographyVariant" Value="Label3"/>
    </Style>

    <!-- Body 样式变体 -->
    <Style x:Key="FluentTextBlockBody1" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeBody1}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBody1}"/>
        <Setter Property="TypographyVariant" Value="Body1"/>
    </Style>

    <Style x:Key="FluentTextBlockBody2" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeBody2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBody2}"/>
        <Setter Property="TypographyVariant" Value="Body2"/>
    </Style>

    <!-- Caption 样式变体 -->
    <Style x:Key="FluentTextBlockCaption1" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeCaption1}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightCaption1}"/>
        <Setter Property="Foreground" Value="{DynamicResource ThemeTextSecondaryBrush}"/>
        <Setter Property="ColorVariant" Value="Secondary"/>
        <Setter Property="TypographyVariant" Value="Caption1"/>
    </Style>

    <Style x:Key="FluentTextBlockCaption2" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeCaption2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightCaption2}"/>
        <Setter Property="Foreground" Value="{DynamicResource ThemeTextSecondaryBrush}"/>
        <Setter Property="ColorVariant" Value="Secondary"/>
        <Setter Property="TypographyVariant" Value="Caption2"/>
    </Style>

    <!-- ================================ -->
    <!-- 颜色变体样式 (Color Variants)     -->
    <!-- ================================ -->

    <!-- Primary 颜色变体 -->
    <Style x:Key="FluentTextBlockPrimary" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="Foreground" Value="{DynamicResource ThemeTextPrimaryBrush}"/>
        <Setter Property="ColorVariant" Value="Primary"/>
    </Style>

    <!-- Secondary 颜色变体 -->
    <Style x:Key="FluentTextBlockSecondary" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="Foreground" Value="{DynamicResource ThemeTextSecondaryBrush}"/>
        <Setter Property="ColorVariant" Value="Secondary"/>
    </Style>

    <!-- Disabled 颜色变体 -->
    <Style x:Key="FluentTextBlockDisabled" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="Foreground" Value="{DynamicResource ThemeTextDisabledBrush}"/>
        <Setter Property="ColorVariant" Value="Disabled"/>
    </Style>

    <!-- Hint 颜色变体 -->
    <Style x:Key="FluentTextBlockHint" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="Foreground" Value="{DynamicResource ThemeTextHintBrush}"/>
        <Setter Property="ColorVariant" Value="Hint"/>
    </Style>

    <!-- Success 颜色变体 -->
    <Style x:Key="FluentTextBlockSuccess" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="Foreground" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="ColorVariant" Value="Success"/>
    </Style>

    <!-- Warning 颜色变体 -->
    <Style x:Key="FluentTextBlockWarning" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="Foreground" Value="{StaticResource WarningBrush700}"/>
        <Setter Property="ColorVariant" Value="Warning"/>
    </Style>

    <!-- Error 颜色变体 -->
    <Style x:Key="FluentTextBlockError" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="Foreground" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="ColorVariant" Value="Error"/>
    </Style>

    <!-- Info 颜色变体 -->
    <Style x:Key="FluentTextBlockInfo" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="Foreground" Value="{StaticResource InfoBrush}"/>
        <Setter Property="ColorVariant" Value="Info"/>
    </Style>

    <!-- Accent 颜色变体 -->
    <Style x:Key="FluentTextBlockAccent" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="ColorVariant" Value="Accent"/>
        <Setter Property="FontWeightVariant" Value="Medium"/>
    </Style>

    <!-- ================================ -->
    <!-- 字重变体样式 (Font Weight Variants) -->
    <!-- ================================ -->

    <!-- Light 字重变体 -->
    <Style x:Key="FluentTextBlockLight" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontWeight" Value="{StaticResource FontWeightLight}"/>
        <Setter Property="FontWeightVariant" Value="Light"/>
    </Style>

    <!-- Regular 字重变体 -->
    <Style x:Key="FluentTextBlockRegular" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="FontWeightVariant" Value="Regular"/>
    </Style>

    <!-- Medium 字重变体 -->
    <Style x:Key="FluentTextBlockMedium" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="FontWeightVariant" Value="Medium"/>
    </Style>

    <!-- SemiBold 字重变体 -->
    <Style x:Key="FluentTextBlockSemiBold" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="FontWeightVariant" Value="SemiBold"/>
    </Style>

    <!-- Bold 字重变体 -->
    <Style x:Key="FluentTextBlockBold" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontWeight" Value="{StaticResource FontWeightBold}"/>
        <Setter Property="FontWeightVariant" Value="Bold"/>
    </Style>

    <!-- ================================ -->
    <!-- 组合样式变体 (Combined Variants)  -->
    <!-- ================================ -->

    <!-- 成功标题样式 -->
    <Style x:Key="FluentTextBlockSuccessTitle" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource FluentTextBlockTitle2}">
        <Setter Property="Foreground" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="ColorVariant" Value="Success"/>
    </Style>

    <!-- 警告标题样式 -->
    <Style x:Key="FluentTextBlockWarningTitle" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource FluentTextBlockTitle2}">
        <Setter Property="Foreground" Value="{StaticResource WarningBrush700}"/>
        <Setter Property="ColorVariant" Value="Warning"/>
    </Style>

    <!-- 错误标题样式 -->
    <Style x:Key="FluentTextBlockErrorTitle" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource FluentTextBlockTitle2}">
        <Setter Property="Foreground" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="ColorVariant" Value="Error"/>
    </Style>

    <!-- 信息标题样式 -->
    <Style x:Key="FluentTextBlockInfoTitle" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource FluentTextBlockTitle2}">
        <Setter Property="Foreground" Value="{StaticResource InfoBrush}"/>
        <Setter Property="ColorVariant" Value="Info"/>
    </Style>

    <!-- 强调标题样式 -->
    <Style x:Key="FluentTextBlockAccentTitle" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource FluentTextBlockTitle2}">
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="ColorVariant" Value="Accent"/>
        <Setter Property="FontWeightVariant" Value="SemiBold"/>
    </Style>

    <!-- ================================ -->
    <!-- 特殊用途样式 (Special Purpose)    -->
    <!-- ================================ -->

    <!-- 等宽字体样式 -->
    <Style x:Key="FluentTextBlockMonospace" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontFamily" Value="{StaticResource MonospaceFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBody2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBody2}"/>
    </Style>

    <!-- 数字字体样式 -->
    <Style x:Key="FluentTextBlockNumeric" TargetType="controls:FluentTextBlock" BasedOn="{StaticResource {x:Type controls:FluentTextBlock}}">
        <Setter Property="FontFamily" Value="{StaticResource NumericFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBody2}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightRegular}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBody2}"/>
        <Setter Property="TextAlignment" Value="Right"/>
    </Style>

</ResourceDictionary>
