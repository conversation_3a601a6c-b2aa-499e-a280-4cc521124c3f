﻿using System;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using FluentDesignWPF.Themes;
using FluentDesignWPF.Demo.Pages;

namespace FluentDesignWPF.Demo
{
    /// <summary>
    /// FluentDesignWPF 设计系统演示主窗口
    /// 展示完整的色彩系统、字体系统和主题切换功能
    /// </summary>
    public partial class MainWindow : Window
    {
        private bool _isDarkTheme = false;
        private TypographyPage _typographyPage;
        private TextBlockPage _textBlockPage;

        public MainWindow()
        {
            InitializeComponent();
            UpdateThemeButtonText();
            _typographyPage = new TypographyPage();
            _textBlockPage = new TextBlockPage();
        }

        /// <summary>
        /// 主题切换按钮点击事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void ThemeToggleButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _isDarkTheme = !_isDarkTheme;
                var theme = _isDarkTheme ? Theme.Dark : Theme.Light;
                ThemeManager.SetTheme(theme);
                UpdateThemeButtonText();

                MessageBox.Show($"已切换到{(_isDarkTheme ? "深色" : "浅色")}主题", "主题切换", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"主题切换失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 色彩页面按钮点击事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void ColorsPageButton_Click(object sender, RoutedEventArgs e)
        {
            // 显示默认的色彩系统内容
            MainContentControl.Content = CreateColorsContent();
        }

        /// <summary>
        /// 字体页面按钮点击事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void TypographyPageButton_Click(object sender, RoutedEventArgs e)
        {
            MainContentControl.Content = _typographyPage;
        }

        /// <summary>
        /// TextBlock控件页面按钮点击事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void TextBlockPageButton_Click(object sender, RoutedEventArgs e)
        {
            MainContentControl.Content = _textBlockPage;
        }

        /// <summary>
        /// 创建色彩系统展示内容
        /// </summary>
        /// <returns>色彩系统展示的UI元素</returns>
        private UIElement CreateColorsContent()
        {
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled
            };

            var stackPanel = new StackPanel { Margin = new Thickness(32) };

            // 标题
            var title = new TextBlock
            {
                Text = "FluentDesignWPF 色彩系统演示",
                Margin = new Thickness(0, 0, 0, 16)
            };
            title.SetResourceReference(TextBlock.StyleProperty, "Headline2TextStyle");
            stackPanel.Children.Add(title);

            // 说明
            var description = new TextBlock
            {
                Text = "这是一个基于Microsoft Fluent Design System的WPF控件库色彩系统演示。",
                Margin = new Thickness(0, 0, 0, 32)
            };
            description.SetResourceReference(TextBlock.StyleProperty, "Body1TextStyle");
            stackPanel.Children.Add(description);

            // 这里可以添加更多色彩展示内容...

            scrollViewer.Content = stackPanel;
            return scrollViewer;
        }

        /// <summary>
        /// 更新主题切换按钮的文本
        /// </summary>
        private void UpdateThemeButtonText()
        {
            if (ThemeToggleButton != null)
            {
                ThemeToggleButton.Content = _isDarkTheme ? "切换到浅色主题" : "切换到深色主题";
            }
        }
    }
}