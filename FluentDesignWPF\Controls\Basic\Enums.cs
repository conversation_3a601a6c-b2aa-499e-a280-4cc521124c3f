namespace FluentDesignWPF.Controls.Basic
{
    /// <summary>
    /// Typography 变体枚举
    /// 定义基于 Microsoft Fluent Design System 的字体层次结构
    /// </summary>
    public enum TypographyVariant
    {
        /// <summary>
        /// Display 1 - 最大显示字体（68px），用于品牌标题和重要展示
        /// </summary>
        Display1,

        /// <summary>
        /// Display 2 - 大显示字体（60px），用于大标题展示
        /// </summary>
        Display2,

        /// <summary>
        /// Display 3 - 中显示字体（52px），用于中等标题展示
        /// </summary>
        Display3,

        /// <summary>
        /// Headline 1 - 最大标题（40px），用于页面主标题
        /// </summary>
        Headline1,

        /// <summary>
        /// Headline 2 - 大标题（32px），用于重要标题
        /// </summary>
        Headline2,

        /// <summary>
        /// Headline 3 - 中标题（28px），用于章节标题
        /// </summary>
        Headline3,

        /// <summary>
        /// Title 1 - 大章节标题（24px），用于主要章节
        /// </summary>
        Title1,

        /// <summary>
        /// Title 2 - 中章节标题（20px），用于次要章节
        /// </summary>
        Title2,

        /// <summary>
        /// Title 3 - 小章节标题（18px），用于小节标题
        /// </summary>
        Title3,

        /// <summary>
        /// Label 1 - 大标签（16px），用于重要标签和按钮
        /// </summary>
        Label1,

        /// <summary>
        /// Label 2 - 标准标签（14px），用于一般标签
        /// </summary>
        Label2,

        /// <summary>
        /// Label 3 - 小标签（12px），用于小标签
        /// </summary>
        Label3,

        /// <summary>
        /// Body 1 - 大正文（16px），用于重要内容
        /// </summary>
        Body1,

        /// <summary>
        /// Body 2 - 标准正文（14px），用于一般内容（默认）
        /// </summary>
        Body2,

        /// <summary>
        /// Caption 1 - 标准说明文字（12px），用于说明和辅助信息
        /// </summary>
        Caption1,

        /// <summary>
        /// Caption 2 - 小说明文字（10px），用于最小的说明文字
        /// </summary>
        Caption2
    }

    /// <summary>
    /// 颜色变体枚举
    /// 定义语义化的文本颜色变体
    /// </summary>
    public enum ColorVariant
    {
        /// <summary>
        /// 主要文本颜色（默认）
        /// </summary>
        Primary,

        /// <summary>
        /// 次要文本颜色，用于辅助信息
        /// </summary>
        Secondary,

        /// <summary>
        /// 禁用文本颜色，用于不可用状态
        /// </summary>
        Disabled,

        /// <summary>
        /// 提示文本颜色，用于占位符和提示信息
        /// </summary>
        Hint,

        /// <summary>
        /// 成功文本颜色，用于成功状态和正面信息
        /// </summary>
        Success,

        /// <summary>
        /// 警告文本颜色，用于警告状态和注意信息
        /// </summary>
        Warning,

        /// <summary>
        /// 错误文本颜色，用于错误状态和负面信息
        /// </summary>
        Error,

        /// <summary>
        /// 信息文本颜色，用于一般信息提示
        /// </summary>
        Info,

        /// <summary>
        /// 强调文本颜色，用于重要信息和链接
        /// </summary>
        Accent
    }

    /// <summary>
    /// 字重变体枚举
    /// 定义不同的字体粗细级别
    /// </summary>
    public enum FontWeightVariant
    {
        /// <summary>
        /// 细体（300），用于大标题和优雅显示
        /// </summary>
        Light,

        /// <summary>
        /// 常规体（400），用于正文内容（默认）
        /// </summary>
        Regular,

        /// <summary>
        /// 中等体（500），用于标签和重要文本
        /// </summary>
        Medium,

        /// <summary>
        /// 半粗体（600），用于小标题和强调
        /// </summary>
        SemiBold,

        /// <summary>
        /// 粗体（700），用于重要标题和强调
        /// </summary>
        Bold
    }
}
