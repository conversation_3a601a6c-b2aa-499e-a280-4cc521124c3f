# FluentSystemDesign WPF 字体系统

FluentSystemDesign WPF控件库提供了基于Microsoft Fluent Design System的完整字体层次结构，确保在不同平台和设备上的一致性显示效果。

## 概述

字体系统包含以下核心特性：

- **完整的字体层次结构**：从Caption（10px）到Display（68px）的全套字体规格
- **多种字体粗细**：Light、Regular、Medium、SemiBold、Bold
- **优化的行高和字符间距**：确保最佳的可读性
- **主题适配**：支持深色和浅色主题的字体颜色
- **DPI适配**：在不同DPI设置下保持清晰度
- **无障碍支持**：符合WCAG对比度标准

## 字体层次结构

### Display 字体样式
用于品牌标题和重要展示，字体大小最大，视觉冲击力强。

| 样式 | 大小 | 字重 | 行高 | 用途 |
|------|------|------|------|------|
| Display1TextStyle | 68px | Light | 76px | 品牌标题、重要展示 |
| Display2TextStyle | 60px | Light | 68px | 大标题展示 |
| Display3TextStyle | 52px | Regular | 60px | 中等标题展示 |

**使用示例：**
```xml
<TextBlock Text="品牌标题" Style="{StaticResource Display1TextStyle}"/>
```

### Headline 字体样式
用于页面标题和重要标题，建立清晰的信息层次。

| 样式 | 大小 | 字重 | 行高 | 用途 |
|------|------|------|------|------|
| Headline1TextStyle | 40px | Regular | 48px | 页面主标题 |
| Headline2TextStyle | 32px | Regular | 40px | 大标题 |
| Headline3TextStyle | 28px | Medium | 36px | 中标题 |

**使用示例：**
```xml
<TextBlock Text="页面标题" Style="{StaticResource Headline1TextStyle}"/>
```

### Title 字体样式
用于章节标题和卡片标题，组织内容结构。

| 样式 | 大小 | 字重 | 行高 | 用途 |
|------|------|------|------|------|
| Title1TextStyle | 24px | Medium | 32px | 大章节标题 |
| Title2TextStyle | 20px | Medium | 28px | 中章节标题 |
| Title3TextStyle | 18px | SemiBold | 26px | 小章节标题 |

**使用示例：**
```xml
<TextBlock Text="章节标题" Style="{StaticResource Title1TextStyle}"/>
```

### Label 字体样式
用于标签和按钮文本，提供清晰的操作指引。

| 样式 | 大小 | 字重 | 行高 | 用途 |
|------|------|------|------|------|
| Label1TextStyle | 16px | Medium | 24px | 大标签、重要按钮 |
| Label2TextStyle | 14px | Medium | 20px | 标准标签、按钮 |
| Label3TextStyle | 12px | SemiBold | 16px | 小标签、辅助按钮 |

**使用示例：**
```xml
<TextBlock Text="按钮标签" Style="{StaticResource Label2TextStyle}"/>
```

### Body 字体样式
用于正文内容，确保良好的阅读体验。

| 样式 | 大小 | 字重 | 行高 | 用途 |
|------|------|------|------|------|
| Body1TextStyle | 16px | Regular | 24px | 重要正文内容 |
| Body2TextStyle | 14px | Regular | 20px | 标准正文内容 |

**使用示例：**
```xml
<TextBlock Text="正文内容" Style="{StaticResource Body2TextStyle}"/>
```

### Caption 字体样式
用于说明文字和辅助信息，提供额外的上下文。

| 样式 | 大小 | 字重 | 行高 | 用途 |
|------|------|------|------|------|
| Caption1TextStyle | 12px | Regular | 16px | 标准说明文字 |
| Caption2TextStyle | 10px | Regular | 14px | 小说明文字、版权信息 |

**使用示例：**
```xml
<TextBlock Text="说明文字" Style="{StaticResource Caption1TextStyle}"/>
```

## 语义化字体样式

基于内容语义定义的字体样式，用于不同状态和用途的文本显示。

| 样式 | 颜色 | 用途 |
|------|------|------|
| PrimaryTextStyle | 主要文本色 | 重要内容 |
| SecondaryTextStyle | 次要文本色 | 辅助信息 |
| DisabledTextStyle | 禁用文本色 | 不可用状态 |
| HintTextStyle | 提示文本色 | 占位符和提示 |
| AccentTextStyle | 强调色 | 重点突出 |
| SuccessTextStyle | 成功色 | 成功状态 |
| WarningTextStyle | 警告色 | 警告信息 |
| ErrorTextStyle | 错误色 | 错误状态 |
| InfoTextStyle | 信息色 | 一般信息 |

**使用示例：**
```xml
<TextBlock Text="成功信息" Style="{StaticResource SuccessTextStyle}"/>
<TextBlock Text="错误信息" Style="{StaticResource ErrorTextStyle}"/>
```

## 特殊用途字体样式

针对特定用途设计的字体样式。

| 样式 | 特点 | 用途 |
|------|------|------|
| MonospaceTextStyle | 等宽字体 | 代码显示 |
| NumericTextStyle | 数字对齐 | 数字显示 |
| LinkTextStyle | 下划线、手型光标 | 链接文本 |
| ButtonTextStyle | 居中对齐 | 按钮文本 |
| ToolTipTextStyle | 最大宽度限制 | 工具提示 |

**使用示例：**
```xml
<TextBlock Text="function() { return 'Hello'; }" Style="{StaticResource MonospaceTextStyle}"/>
<TextBlock Text="123,456.78" Style="{StaticResource NumericTextStyle}"/>
```

## 字体族定义

控件库定义了三种字体族，确保在不同平台上的一致性：

### 主要字体族 (PrimaryFontFamily)
```
Segoe UI, Microsoft YaHei UI, PingFang SC, Helvetica Neue, Arial, sans-serif
```
用于大部分文本显示，优先使用系统字体。

### 等宽字体族 (MonospaceFontFamily)
```
Consolas, SF Mono, Monaco, Inconsolata, Fira Code, Droid Sans Mono, Courier New, monospace
```
用于代码显示和需要字符对齐的场景。

### 数字字体族 (NumericFontFamily)
```
Segoe UI, Roboto Mono, SF Mono, Consolas, monospace
```
用于数字显示，确保数字的等宽对齐。

## 常用别名

为了方便使用，控件库提供了常用的字体样式别名：

| 别名 | 对应样式 | 用途 |
|------|----------|------|
| DefaultTextStyle | Body2TextStyle | 默认文本 |
| TitleTextStyle | Title2TextStyle | 标题 |
| SubtitleTextStyle | Title3TextStyle | 副标题 |
| BodyTextStyle | Body2TextStyle | 正文 |
| LargeBodyTextStyle | Body1TextStyle | 大正文 |
| CaptionTextStyle | Caption1TextStyle | 说明 |
| SmallCaptionTextStyle | Caption2TextStyle | 小说明 |
| LabelTextStyle | Label2TextStyle | 标签 |
| LargeLabelTextStyle | Label1TextStyle | 大标签 |
| SmallLabelTextStyle | Label3TextStyle | 小标签 |

## 使用指南

### 1. 选择合适的字体层次

- **Display**: 用于品牌展示和最重要的标题
- **Headline**: 用于页面级别的标题
- **Title**: 用于章节和组件标题
- **Label**: 用于控件标签和按钮
- **Body**: 用于正文内容
- **Caption**: 用于辅助说明

### 2. 语义化使用

根据内容的语义和状态选择合适的字体样式：

```xml
<!-- 成功状态 -->
<TextBlock Text="操作成功" Style="{StaticResource SuccessTextStyle}"/>

<!-- 错误状态 -->
<TextBlock Text="操作失败" Style="{StaticResource ErrorTextStyle}"/>

<!-- 禁用状态 -->
<TextBlock Text="不可用" Style="{StaticResource DisabledTextStyle}"/>
```

### 3. 主题适配

使用DynamicResource引用字体样式以支持主题切换：

```xml
<TextBlock Text="主题适配文本" 
           Foreground="{DynamicResource PrimaryTextBrush}"
           Style="{StaticResource Body2TextStyle}"/>
```

### 4. 特殊用途

针对特定场景使用专门的字体样式：

```xml
<!-- 代码显示 -->
<TextBlock Text="var result = function();" Style="{StaticResource MonospaceTextStyle}"/>

<!-- 数字显示 -->
<TextBlock Text="1,234.56" Style="{StaticResource NumericTextStyle}"/>

<!-- 链接 -->
<TextBlock Text="了解更多" Style="{StaticResource LinkTextStyle}"/>
```

## 最佳实践

### 1. 保持一致性
- 在同一应用中保持字体层次的一致性
- 避免在同一页面使用过多不同的字体样式
- 遵循既定的字体层次结构

### 2. 可读性优先
- 确保文本在深色和浅色主题下都有良好的可读性
- 考虑不同DPI设置下的字体清晰度
- 为长文本内容选择合适的行高

### 3. 语义化命名
- 优先使用语义化的字体样式（Primary、Secondary等）
- 根据内容的重要性和用途选择字体样式
- 使用特殊用途样式处理特定场景

### 4. 性能考虑
- 使用StaticResource引用不需要主题切换的字体样式
- 使用DynamicResource引用需要主题切换的字体样式
- 避免在代码中硬编码字体属性

## 无障碍支持

字体系统符合WCAG无障碍访问标准：

- **对比度**: 所有文本颜色都满足WCAG AA级对比度要求
- **字体大小**: 最小字体大小为10px，确保可读性
- **字体粗细**: 合理的字体粗细确保在不同设备上的清晰显示
- **行高**: 适当的行高提高文本的可读性

## 示例应用

在示例项目中，您可以查看完整的字体系统展示：

1. 运行FluentDesignWPF.Demo项目
2. 点击"字体系统"按钮
3. 查看所有字体样式的实际效果
4. 测试主题切换下的字体颜色变化

## 技术实现

字体系统的技术实现细节：

- **资源字典**: Typography.xaml定义了所有字体相关资源
- **样式继承**: 使用BasedOn实现样式的继承和复用
- **主题集成**: 与Colors.xaml和Brushes.xaml协同工作
- **DPI适配**: 使用UseLayoutRounding和TextOptions优化显示效果

---

更多信息请参考：
- [主README文档](../README.md)
- [色彩系统文档](Colors.md)
- [控件文档](Controls/)
