{"Version": 1, "WorkspaceRootPath": "D:\\Project\\04  FluentDesignWPF\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{226D247F-EDA4-4BCA-8384-F69A0D09E6D5}|FluentDesignWPF\\FluentDesignWPF.csproj|d:\\project\\04  fluentdesignwpf\\fluentdesignwpf\\themes\\controls\\textblock.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{226D247F-EDA4-4BCA-8384-F69A0D09E6D5}|FluentDesignWPF\\FluentDesignWPF.csproj|solutionrelative:fluentdesignwpf\\themes\\controls\\textblock.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{FFBA3543-130C-4388-BAFF-AE5F2534EB87}|FluentDesignWPF.Demo\\FluentDesignWPF.Demo.csproj|d:\\project\\04  fluentdesignwpf\\fluentdesignwpf.demo\\pages\\textblockpage.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{FFBA3543-130C-4388-BAFF-AE5F2534EB87}|FluentDesignWPF.Demo\\FluentDesignWPF.Demo.csproj|solutionrelative:fluentdesignwpf.demo\\pages\\textblockpage.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{FFBA3543-130C-4388-BAFF-AE5F2534EB87}|FluentDesignWPF.Demo\\FluentDesignWPF.Demo.csproj|d:\\project\\04  fluentdesignwpf\\fluentdesignwpf.demo\\pages\\typographypage.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{FFBA3543-130C-4388-BAFF-AE5F2534EB87}|FluentDesignWPF.Demo\\FluentDesignWPF.Demo.csproj|solutionrelative:fluentdesignwpf.demo\\pages\\typographypage.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{226D247F-EDA4-4BCA-8384-F69A0D09E6D5}|FluentDesignWPF\\FluentDesignWPF.csproj|d:\\project\\04  fluentdesignwpf\\fluentdesignwpf\\themes\\typography.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{226D247F-EDA4-4BCA-8384-F69A0D09E6D5}|FluentDesignWPF\\FluentDesignWPF.csproj|solutionrelative:fluentdesignwpf\\themes\\typography.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{FFBA3543-130C-4388-BAFF-AE5F2534EB87}|FluentDesignWPF.Demo\\FluentDesignWPF.Demo.csproj|d:\\project\\04  fluentdesignwpf\\fluentdesignwpf.demo\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{FFBA3543-130C-4388-BAFF-AE5F2534EB87}|FluentDesignWPF.Demo\\FluentDesignWPF.Demo.csproj|solutionrelative:fluentdesignwpf.demo\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\45295768691f8390fbec6c7c6e738e6ef19358a6d26c1990f835aac52db50d35\\XamlReader.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{226D247F-EDA4-4BCA-8384-F69A0D09E6D5}|FluentDesignWPF\\FluentDesignWPF.csproj|d:\\project\\04  fluentdesignwpf\\fluentdesignwpf\\themes\\colors.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{226D247F-EDA4-4BCA-8384-F69A0D09E6D5}|FluentDesignWPF\\FluentDesignWPF.csproj|solutionrelative:fluentdesignwpf\\themes\\colors.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{226D247F-EDA4-4BCA-8384-F69A0D09E6D5}|FluentDesignWPF\\FluentDesignWPF.csproj|d:\\project\\04  fluentdesignwpf\\fluentdesignwpf\\themes\\brushes.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{226D247F-EDA4-4BCA-8384-F69A0D09E6D5}|FluentDesignWPF\\FluentDesignWPF.csproj|solutionrelative:fluentdesignwpf\\themes\\brushes.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "TextBlockPage.xaml", "DocumentMoniker": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF.Demo\\Pages\\TextBlockPage.xaml", "RelativeDocumentMoniker": "FluentDesignWPF.Demo\\Pages\\TextBlockPage.xaml", "ToolTip": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF.Demo\\Pages\\TextBlockPage.xaml", "RelativeToolTip": "FluentDesignWPF.Demo\\Pages\\TextBlockPage.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-20T23:22:43.753Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "TextBlock.xaml", "DocumentMoniker": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF\\Themes\\Controls\\TextBlock.xaml", "RelativeDocumentMoniker": "FluentDesignWPF\\Themes\\Controls\\TextBlock.xaml", "ToolTip": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF\\Themes\\Controls\\TextBlock.xaml", "RelativeToolTip": "FluentDesignWPF\\Themes\\Controls\\TextBlock.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-20T23:22:09.071Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "Typography.xaml", "DocumentMoniker": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF\\Themes\\Typography.xaml", "RelativeDocumentMoniker": "FluentDesignWPF\\Themes\\Typography.xaml", "ToolTip": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF\\Themes\\Typography.xaml", "RelativeToolTip": "FluentDesignWPF\\Themes\\Typography.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-20T23:12:19.968Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "TypographyPage.xaml", "DocumentMoniker": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF.Demo\\Pages\\TypographyPage.xaml", "RelativeDocumentMoniker": "FluentDesignWPF.Demo\\Pages\\TypographyPage.xaml", "ToolTip": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF.Demo\\Pages\\TypographyPage.xaml", "RelativeToolTip": "FluentDesignWPF.Demo\\Pages\\TypographyPage.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-20T23:12:09.612Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF.Demo\\MainWindow.xaml", "RelativeDocumentMoniker": "FluentDesignWPF.Demo\\MainWindow.xaml", "ToolTip": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF.Demo\\MainWindow.xaml", "RelativeToolTip": "FluentDesignWPF.Demo\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-20T22:53:32.737Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "XamlReader.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\45295768691f8390fbec6c7c6e738e6ef19358a6d26c1990f835aac52db50d35\\XamlReader.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\45295768691f8390fbec6c7c6e738e6ef19358a6d26c1990f835aac52db50d35\\XamlReader.cs [只读]", "ViewState": "AgIAAHcBAAAAAAAAAAAuwIUBAAAAAAAAAQAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T22:50:23.824Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 6, "Title": "Colors.xaml", "DocumentMoniker": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF\\Themes\\Colors.xaml", "RelativeDocumentMoniker": "FluentDesignWPF\\Themes\\Colors.xaml", "ToolTip": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF\\Themes\\Colors.xaml", "RelativeToolTip": "FluentDesignWPF\\Themes\\Colors.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-20T22:28:34.969Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "Brushes.xaml", "DocumentMoniker": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF\\Themes\\Brushes.xaml", "RelativeDocumentMoniker": "FluentDesignWPF\\Themes\\Brushes.xaml", "ToolTip": "D:\\Project\\04  FluentDesignWPF\\FluentDesignWPF\\Themes\\Brushes.xaml", "RelativeToolTip": "FluentDesignWPF\\Themes\\Brushes.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-20T22:28:23.796Z", "EditorCaption": ""}]}]}]}